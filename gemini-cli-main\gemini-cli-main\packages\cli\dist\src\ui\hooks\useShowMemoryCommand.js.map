{"version": 3, "file": "useShowMemoryCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useShowMemoryCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAW,WAAW,EAAE,MAAM,aAAa,CAAC;AAInD,MAAM,UAAU,sBAAsB,CACpC,MAAqB,EACrB,QAAwB,EACxB,UAAsC;IAEtC,OAAO,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,UAAU,CAAC;gBACT,IAAI,EAAE,WAAW,CAAC,KAAK;gBACvB,OAAO,EAAE,kDAAkD;gBAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QAExC,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAChD,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;QACxD,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;YACrD,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAEtB,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CACT,kFAAkF,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CACvH,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;YAC3D,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC/D,UAAU,CAAC;gBACT,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,OAAO,EAAE,sBAAsB,SAAS,IAAI,IAAI,QAC9C,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EACxB,GAAG;gBACH,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,UAAU,CAAC;gBACT,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,OAAO,EAAE,qDAAqD,aAAa,UAAU;gBACrF,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,UAAU,CAAC;gBACT,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,OAAO,EACL,SAAS,GAAG,CAAC;oBACX,CAAC,CAAC,wFAAwF;oBAC1F,CAAC,CAAC,gFAAgF;gBACtF,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC"}