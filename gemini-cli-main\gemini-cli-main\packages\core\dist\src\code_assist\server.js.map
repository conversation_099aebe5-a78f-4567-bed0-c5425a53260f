{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../../src/code_assist/server.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAmBH,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AAErC,OAAO,EAGL,sBAAsB,EACtB,2BAA2B,EAC3B,mBAAmB,EACnB,wBAAwB,GACzB,MAAM,gBAAgB,CAAC;AASxB,8DAA8D;AAC9D,MAAM,CAAC,MAAM,oBAAoB,GAAG,qCAAqC,CAAC;AAC1E,MAAM,CAAC,MAAM,uBAAuB,GAAG,YAAY,CAAC;AAEpD,MAAM,OAAO,gBAAgB;IAEhB;IACA;IACA;IACA;IAJX,YACW,MAAoB,EACpB,SAAkB,EAClB,cAA2B,EAAE,EAC7B,SAAkB;QAHlB,WAAM,GAAN,MAAM,CAAc;QACpB,cAAS,GAAT,SAAS,CAAS;QAClB,gBAAW,GAAX,WAAW,CAAkB;QAC7B,cAAS,GAAT,SAAS,CAAS;IAC1B,CAAC;IAEJ,KAAK,CAAC,qBAAqB,CACzB,GAA8B;QAE9B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAC3C,uBAAuB,EACvB,wBAAwB,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAC7D,GAAG,CAAC,MAAM,EAAE,WAAW,CACxB,CAAC;QACF,OAAO,CAAC,KAAK,SAAS,CAAC;YACrB,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC/B,MAAM,2BAA2B,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;IACP,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,GAA8B;QAE9B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CACjC,iBAAiB,EACjB,wBAAwB,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAC7D,GAAG,CAAC,MAAM,EAAE,WAAW,CACxB,CAAC;QACF,OAAO,2BAA2B,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,WAAW,CACf,GAAuB;QAEvB,OAAO,MAAM,IAAI,CAAC,WAAW,CAC3B,aAAa,EACb,GAAG,CACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,GAA0B;QAE1B,OAAO,MAAM,IAAI,CAAC,WAAW,CAC3B,gBAAgB,EAChB,GAAG,CACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,8BAA8B;QAClC,OAAO,MAAM,IAAI,CAAC,UAAU,CAC1B,gCAAgC,CACjC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,8BAA8B,CAClC,GAA0C;QAE1C,OAAO,MAAM,IAAI,CAAC,WAAW,CAC3B,gCAAgC,EAChC,GAAG,CACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAA0B;QAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CACjC,aAAa,EACb,mBAAmB,CAAC,GAAG,CAAC,CACzB,CAAC;QACF,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,IAA4B;QAE5B,MAAM,KAAK,EAAE,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAAc,EACd,GAAW,EACX,MAAoB;QAEpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC9B,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;aAC5B;YACD,YAAY,EAAE,MAAM;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACzB,MAAM;SACP,CAAC,CAAC;QACH,OAAO,GAAG,CAAC,IAAS,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,UAAU,CAAI,MAAc,EAAE,MAAoB;QACtD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC9B,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;aAC5B;YACD,YAAY,EAAE,MAAM;YACpB,MAAM;SACP,CAAC,CAAC;QACH,OAAO,GAAG,CAAC,IAAS,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAAc,EACd,GAAW,EACX,MAAoB;QAEpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC9B,MAAM,EAAE,MAAM;YACd,MAAM,EAAE;gBACN,GAAG,EAAE,KAAK;aACX;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;aAC5B;YACD,YAAY,EAAE,QAAQ;YACtB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACzB,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,SAAS,CAAC;YACrB,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;gBAClC,KAAK,EAAE,GAAG,CAAC,IAAmB;gBAC9B,SAAS,EAAE,QAAQ,EAAE,4CAA4C;aAClE,CAAC,CAAC;YAEH,IAAI,aAAa,GAAa,EAAE,CAAC;YACjC,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,EAAE,EAAE,CAAC;gBAC5B,8DAA8D;gBAC9D,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;oBAChB,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC/B,SAAS,CAAC,mBAAmB;oBAC/B,CAAC;oBACD,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAM,CAAC;oBAChD,aAAa,GAAG,EAAE,CAAC,CAAC,kCAAkC;gBACxD,CAAC;qBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;IACP,CAAC;IAED,YAAY,CAAC,MAAc;QACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,oBAAoB,CAAC;QAC1E,OAAO,GAAG,QAAQ,IAAI,uBAAuB,IAAI,MAAM,EAAE,CAAC;IAC5D,CAAC;CACF"}