# Assets Directory

This directory contains application assets such as icons and images.

## Required Files

For proper application building, you'll need:

1. **icon.png** - Main application icon (512x512 recommended)
2. **icon.ico** - Windows icon file
3. **icon.icns** - macOS icon file

## Creating Icons

You can create these icons from a single PNG file using online tools or:

### Using electron-icon-builder (recommended)
```bash
npm install -g electron-icon-builder
electron-icon-builder --input=./icon.png --output=./assets --flatten
```

### Manual Creation
- **PNG**: 512x512 pixels, transparent background
- **ICO**: Use online converters or tools like ImageMagick
- **ICNS**: Use online converters or macOS tools

## Temporary Solution

For now, the application will use a default icon. You can replace these files with your custom icons later.
