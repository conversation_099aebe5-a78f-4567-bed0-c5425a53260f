{"version": 3, "file": "oauth2.js", "sourceRoot": "", "sources": ["../../../src/code_assist/oauth2.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,YAAY,EAAe,MAAM,qBAAqB,CAAC;AAChE,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,GAAG,MAAM,KAAK,CAAC;AACtB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAC3B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AACnE,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB,wDAAwD;AACxD,MAAM,eAAe,GACnB,0EAA0E,CAAC;AAE7E,0DAA0D;AAC1D,6EAA6E;AAC7E,uFAAuF;AACvF,2EAA2E;AAC3E,4EAA4E;AAC5E,4DAA4D;AAC5D,MAAM,mBAAmB,GAAG,qCAAqC,CAAC;AAElE,6CAA6C;AAC7C,MAAM,WAAW,GAAG;IAClB,gDAAgD;IAChD,gDAAgD;IAChD,kDAAkD;CACnD,CAAC;AAEF,MAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,MAAM,mBAAmB,GACvB,sEAAsE,CAAC;AACzE,MAAM,mBAAmB,GACvB,sEAAsE,CAAC;AAEzE,MAAM,UAAU,GAAG,SAAS,CAAC;AAC7B,MAAM,mBAAmB,GAAG,kBAAkB,CAAC;AAC/C,MAAM,0BAA0B,GAAG,mBAAmB,CAAC;AAYvD,MAAM,CAAC,KAAK,UAAU,cAAc;IAClC,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC;QAC9B,QAAQ,EAAE,eAAe;QACzB,YAAY,EAAE,mBAAmB;KAClC,CAAC,CAAC;IACH,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAmB,EAAE,EAAE;QAChD,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,IAAI,MAAM,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,kCAAkC;QAClC,iDAAiD;QACjD,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAC5D,IAAI,eAAe,EAAE,CAAC;oBACpB,MAAM,oBAAoB,CAAC,eAAe,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CACX,gEAAgE,EAChE,KAAK,CACN,CAAC;gBACF,mCAAmC;YACrC,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,CAAC;IAE3C,OAAO,CAAC,GAAG,CACT,mCAAmC;QACjC,2DAA2D;QAC3D,6BAA6B,QAAQ,CAAC,OAAO,MAAM,CACtD,CAAC;IACF,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAE7C,MAAM,QAAQ,CAAC,oBAAoB,CAAC;IAEpC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,MAAoB;IAC7C,MAAM,IAAI,GAAG,MAAM,gBAAgB,EAAE,CAAC;IACtC,MAAM,WAAW,GAAG,oBAAoB,IAAI,iBAAiB,CAAC;IAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrD,MAAM,OAAO,GAAW,MAAM,CAAC,eAAe,CAAC;QAC7C,YAAY,EAAE,WAAW;QACzB,WAAW,EAAE,SAAS;QACtB,KAAK,EAAE,WAAW;QAClB,KAAK;KACN,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAI,CAAC;gBACH,IAAI,GAAG,CAAC,GAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC/C,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,GAAG,EAAE,CAAC;oBACV,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtD,CAAC;gBACD,mEAAmE;gBACnE,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAI,EAAE,uBAAuB,CAAC,CAAC,YAAY,CAAC;gBACvE,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBACpB,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,GAAG,EAAE,CAAC;oBAEV,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvE,CAAC;qBAAM,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,KAAK,EAAE,CAAC;oBACrC,GAAG,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;oBAEhD,MAAM,CAAC,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC;gBAC5D,CAAC;qBAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC;wBACvC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAE;wBACrB,YAAY,EAAE,WAAW;qBAC1B,CAAC,CAAC;oBACH,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;oBAC9B,6DAA6D;oBAC7D,IAAI,CAAC;wBACH,MAAM,eAAe,GAAG,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAC;wBAC5D,IAAI,eAAe,EAAE,CAAC;4BACpB,MAAM,oBAAoB,CAAC,eAAe,CAAC,CAAC;wBAC9C,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CACX,6DAA6D,EAC7D,KAAK,CACN,CAAC;wBACF,gEAAgE;oBAClE,CAAC;oBAED,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,GAAG,EAAE,CAAC;oBACV,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,OAAO;QACP,oBAAoB;KACrB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gBAAgB;IAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE;gBACpB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAsB,CAAC;gBACrD,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;gBAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,MAAoB;IACvD,IAAI,CAAC;QACH,MAAM,OAAO,GACX,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,uBAAuB,EAAE,CAAC;QAE1E,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAEzC,2DAA2D;QAC3D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;QAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oEAAoE;QACpE,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,WAAwB;IACtD,MAAM,QAAQ,GAAG,uBAAuB,EAAE,CAAC;IAC3C,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAE5D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACxD,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,uBAAuB;IAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,2BAA2B;IAClC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,0BAA0B,CAAC,CAAC;AACzE,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,eAAuB;IACzD,MAAM,QAAQ,GAAG,2BAA2B,EAAE,CAAC;IAC/C,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5D,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,MAAM,UAAU,wBAAwB;IACtC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,2BAA2B,EAAE,CAAC;QAC/C,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,OAAO,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;QACxD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,IAAI,CAAC;QACH,MAAM,EAAE,CAAC,EAAE,CAAC,uBAAuB,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACxD,iEAAiE;QACjE,MAAM,EAAE,CAAC,EAAE,CAAC,2BAA2B,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,WAAW;IACb,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAAoB;IAEpB,IAAI,CAAC;QACH,mDAAmD;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,OAAO,CACvC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClB,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACxC,IAAI,GAAG,EAAE,CAAC;oBACR,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;gBACD,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,uEAAuE;QACvE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC;YACxC,OAAO,EAAE,eAAe,CAAC,QAAQ;YACjC,QAAQ,EAAE,eAAe;SAC1B,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QACpC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,OAAO,CAAC,GAAG,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC"}