const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  try {
    fs.mkdirSync(logsDir, { recursive: true });
  } catch (err) {
    console.error(`Failed to create logs directory: ${err.message}`);
  }
}

// Log file paths
const ERROR_LOG_PATH = path.join(logsDir, 'error.log');
const INFO_LOG_PATH = path.join(logsDir, 'info.log');

// Log levels with corresponding colors
const LEVELS = {
  ERROR: { color: chalk.red.bold, prefix: '❌ ERROR' },
  WARN: { color: chalk.yellow, prefix: '⚠️ WARN' },
  INFO: { color: chalk.blue, prefix: 'ℹ️ INFO' },
  SUCCESS: { color: chalk.green, prefix: '✅ SUCCESS' },
  USER: { color: chalk.cyan, prefix: '👤 USER' },
  API: { color: chalk.magenta, prefix: '🔌 API' },
  DEBUG: { color: chalk.gray, prefix: '🔍 DEBUG' },
  QUESTION: { color: chalk.gray, prefix: '📝 QUESTION' }
};

/**
 * Logger utility for consistent, colored console logging
 */
class Logger {
  constructor() {
    this.debugEnabled = process.env.DEBUG_MODE === 'true';
    this.logQuestionData = process.env.LOG_QUESTION_DATA === 'true';

    // Cache to avoid duplicate API logs
    this.recentApiLogs = new Map();
    this.apiLogExpiry = 1000; // 1 second expiry for duplicate API logs

    // Track request IDs to avoid duplicate logs
    this.seenRequestIds = new Set();

    // Cleanup intervals to prevent memory leaks
    setInterval(() => {
      this.cleanupCaches();
    }, 60000); // Clean every minute
  }

  /**
   * Clean up caches to prevent memory leaks
   */
  cleanupCaches() {
    const now = Date.now();

    // Clean up recent API logs
    for (const [key, timestamp] of this.recentApiLogs.entries()) {
      if (now - timestamp > this.apiLogExpiry * 10) { // Keep for 10x expiry time
        this.recentApiLogs.delete(key);
      }
    }

    // Clean up seen request IDs (keep only recent ones)
    if (this.seenRequestIds.size > 1000) {
      this.seenRequestIds.clear();
    }
  }

  /**
   * Internal log method with timestamp
   */
  _log(level, message, data = null) {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const logConfig = LEVELS[level];
    
    // Format: [TIME] PREFIX: Message
    const logMessage = `[${timestamp}] ${logConfig.prefix}: ${message}`;
    
    // Apply color and log to console
    console.log(logConfig.color(logMessage));
    
    // Log additional data if provided (without color to keep it readable)
    if (data && this.debugEnabled) {
      if (typeof data === 'object') {
        console.log(JSON.stringify(data, null, 2));
      } else {
        console.log(data);
      }
    }
    
    // Also log to file for certain levels
    this._logToFile(level, timestamp, message, data);
  }
  
  /**
   * Log to file based on level
   */
  _logToFile(level, timestamp, message, data) {
    try {
      let logFilePath = null;
      
      // Determine which log file to use
      if (level === 'ERROR' || level === 'WARN') {
        logFilePath = ERROR_LOG_PATH;
      } else if (['INFO', 'SUCCESS', 'USER'].includes(level)) {
        logFilePath = INFO_LOG_PATH;
      }
      
      // If we have a file path, write the log
      if (logFilePath) {
        const fileLogMessage = `[${timestamp}] ${LEVELS[level].prefix}: ${message}`;
        
        // Add data object if available 
        let fileOutput = fileLogMessage;
        if (data && typeof data === 'object') {
          fileOutput += `\n${JSON.stringify(data, null, 2)}`;
        } else if (data) {
          fileOutput += `\n${data}`;
        }
        
        // Append to log file with a newline (use async to prevent blocking)
        fs.appendFile(logFilePath, fileOutput + '\n', (err) => {
          if (err) {
            console.error(`Failed to write to log file: ${err.message}`);
          }
        });
      }
    } catch (err) {
      // Just output to console if file logging fails, avoid infinite recursion by using console directly
      console.error(`Failed to write to log file: ${err.message}`);
    }
  }

  // Error logs
  error(message, data = null) {
    this._log('ERROR', message, data);
  }

  // Warning logs
  warn(message, data = null) {
    this._log('WARN', message, data);
  }

  // Info logs
  info(message, data = null) {
    this._log('INFO', message, data);
  }

  // Success logs
  success(message, data = null) {
    this._log('SUCCESS', message, data);
  }

  // User action logs
  user(message, data = null) {
    this._log('USER', message, data);
  }

  // API call logs with deduplication
  api(message, data = null) {
    // Extract request ID from message if present
    const requestIdMatch = message.match(/\[([0-9a-f]+)\]/);
    
    if (requestIdMatch) {
      const requestId = requestIdMatch[1];
      
      // Get message type (general part after the request ID)
      let messageType = message;
      if (message.includes('Request queued:')) messageType = 'queue';
      else if (message.includes('Executing API request')) messageType = 'execute';
      else if (message.includes('Received response')) messageType = 'response';
      else if (message.includes('Duplicate request')) messageType = 'duplicate';
      else if (message.includes('Request complete')) messageType = 'complete';
      else if (message.includes('Request failed')) messageType = 'failed';
      
      // Create a key from request ID and message type
      const logKey = `${requestId}:${messageType}`;
      
      // Check if we've seen this exact request ID and message type recently
      if (this.recentApiLogs.has(logKey)) {
        // Skip duplicate logs that happen within the expiry period
        return;
      }
      
      // Record this log and set an expiry
      this.recentApiLogs.set(logKey, Date.now());
      
      // Clean up old entries from the map
      this._cleanupApiLogCache();
    }
    
    // Log the message
    this._log('API', message, data);
  }
  
  // Clean up expired API log cache entries
  _cleanupApiLogCache() {
    const now = Date.now();
    for (const [key, timestamp] of this.recentApiLogs.entries()) {
      if (now - timestamp > this.apiLogExpiry) {
        this.recentApiLogs.delete(key);
      }
    }
  }

  // Debug logs - only shown when DEBUG_MODE is true
  debug(message, data = null) {
    if (this.debugEnabled) {
      this._log('DEBUG', message, data);
    }
  }

  // Question logs - only shown when LOG_QUESTION_DATA is true
  question(message, data = null) {
    if (this.logQuestionData) {
      this._log('QUESTION', message, data);
    }
  }

  // Filter messages to hide sensitive/verbose content
  filterSensitiveLog(message) {
    // Hide question text details
    if (message.includes('Question data for index') || 
        message.includes('correct answer is')) {
      return false;
    }
    return true;
  }
}

module.exports = new Logger(); 