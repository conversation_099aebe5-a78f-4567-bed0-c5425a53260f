{"version": 3, "file": "TableRenderer.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/TableRenderer.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAQtC;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAiC,CAAC,EAC1D,OAAO,EACP,IAAI,EACJ,aAAa,GACd,EAAE,EAAE;IACH,0BAA0B;IAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACjD,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAC1B,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAChD,CAAC;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;IAC/D,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,MAAM,WAAW,GACf,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAChD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,CAChC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,OAAe,EAAE,KAAa,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAE;QACtE,kDAAkD;QAClD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QAE5C,IAAI,WAAW,GAAG,OAAO,CAAC;QAC1B,IAAI,OAAO,CAAC,MAAM,GAAG,YAAY,EAAE,CAAC;YAClC,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;gBACtB,6BAA6B;gBAC7B,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAErD,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,CACL,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAChC,MAAM,GACF,CACR,CAAC;QACJ,CAAC;QACD,OAAO,KAAC,IAAI,cAAE,MAAM,GAAQ,CAAC;IAC/B,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,CAAC,KAAe,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAE,CAAC,CACvD,MAAC,GAAG,IAAC,aAAa,EAAC,KAAK,aACtB,KAAC,IAAI,0BAAU,EACd,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAC1B,MAAC,KAAK,CAAC,QAAQ,eACZ,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,EACvD,KAAC,IAAI,2BAAW,KAFG,KAAK,CAGT,CAClB,CAAC,IACE,CACP,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,SAAS,GAAG,cAAc;aAC7B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACzD,IAAI,CAAC,KAAK,CAAC,CAAC;QACf,OAAO,MAAC,IAAI,+BAAI,SAAS,oBAAU,CAAC;IACtC,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,MAAM,GAAG,cAAc;aAC1B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACzD,IAAI,CAAC,KAAK,CAAC,CAAC;QACf,OAAO,MAAC,IAAI,+BAAI,MAAM,oBAAU,CAAC;IACnC,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,MAAM,MAAM,GAAG,cAAc;aAC1B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACzD,IAAI,CAAC,KAAK,CAAC,CAAC;QACf,OAAO,MAAC,IAAI,+BAAI,MAAM,oBAAU,CAAC;IACnC,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aACnC,eAAe,EAAE,EACjB,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,EACxB,eAAe,EAAE,EACjB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CACxB,KAAC,KAAK,CAAC,QAAQ,cAAc,SAAS,CAAC,GAAG,CAAC,IAAtB,KAAK,CAAmC,CAC9D,CAAC,EACD,kBAAkB,EAAE,IACjB,CACP,CAAC;AACJ,CAAC,CAAC"}