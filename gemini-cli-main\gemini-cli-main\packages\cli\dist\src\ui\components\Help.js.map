{"version": 3, "file": "Help.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/Help.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAOtC,MAAM,CAAC,MAAM,IAAI,GAAmB,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACpD,MAAC,GAAG,IACF,aAAa,EAAC,QAAQ,EACtB,YAAY,EAAE,CAAC,EACf,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,WAAW,EAAC,OAAO,EACnB,OAAO,EAAE,CAAC,aAGV,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,wBAE5B,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,4BAE9B,WACD,GAAG,EACT,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,kBAE9B,EAAC,GAAG,yCACyB,GAAG,EACvC,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,+BAE9B,8CAEF,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,2BAE9B,kCACsB,GAAG,EAChC,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,kBAE9B,EAAC,GAAG,YACJ,GAAG,EACV,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,+BAE9B,qCACyB,GAAG,EACnC,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,6BAE9B,UAEF,EAEP,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAGlB,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,0BAE5B,EACN,QAAQ;aACN,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;aACxC,GAAG,CAAC,CAAC,OAAqB,EAAE,EAAE,CAAC,CAC9B,MAAC,IAAI,IAAoB,KAAK,EAAE,MAAM,CAAC,UAAU,aAC/C,MAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,aAClC,GAAG,OACF,OAAO,CAAC,IAAI,IACT,EACN,OAAO,CAAC,WAAW,IAAI,KAAK,GAAG,OAAO,CAAC,WAAW,KAL1C,OAAO,CAAC,IAAI,CAMhB,CACR,CAAC,EACJ,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,MAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,aAClC,GAAG,OACF,GAAG,IACA,uBAEF,EAEP,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAGlB,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,oCAE5B,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,sBAE9B,EAAC,GAAG,sBAEN,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAClC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,GAClD,EAAC,GAAG,EACV,OAAO,CAAC,QAAQ,KAAK,OAAO;oBAC3B,CAAC,CAAC,wDAAwD;oBAC1D,CAAC,CAAC,YAAY,IACX,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,wBAE9B,EAAC,GAAG,2CAEN,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,+BAE9B,EAAC,GAAG,yCAEN,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,0BAE9B,EAAC,GAAG,qCAEN,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,oBAE9B,EAAC,GAAG,0BAEN,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,aAC5B,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,uBAE9B,EAAC,GAAG,0BAEN,IACH,CACP,CAAC"}