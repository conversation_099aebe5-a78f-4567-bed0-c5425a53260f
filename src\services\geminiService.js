const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const axios = require('axios');

class GeminiService {
    constructor() {
        this.isAuthenticated = false;
        this.authStatus = 'not_authenticated';
        this.configPath = path.join(os.homedir(), '.gemini');
        this.currentProcess = null;
        this.authWindow = null;
        this.userInfo = null;

        // Set the correct path to gemini executable
        this.geminiPath = this.getGeminiPath();
    }

    /**
     * Get the correct path to gemini executable
     */
    getGeminiPath() {
        const isWindows = os.platform() === 'win32';
        if (isWindows) {
            // On Windows, use PowerShell to run gemini (more reliable)
            return 'powershell';
        } else {
            // On Unix-like systems, try common locations
            return 'gemini'; // Fallback to PATH lookup
        }
    }

    /**
     * Get spawn arguments for gemini command
     */
    getGeminiArgs(originalArgs = []) {
        const isWindows = os.platform() === 'win32';
        if (isWindows) {
            // On Windows, use PowerShell to run gemini
            return ['-Command', 'gemini', ...originalArgs];
        } else {
            return originalArgs;
        }
    }

    /**
     * Check if user is already authenticated with Gemini CLI
     */
    async checkAuthStatus() {
        try {
            // Check if Gemini CLI config exists
            const configExists = await this.checkConfigExists();
            if (configExists) {
                // Test with a simple query to verify authentication
                const testResult = await this.testAuthentication();
                this.isAuthenticated = testResult;
                this.authStatus = testResult ? 'authenticated' : 'expired';

                // If authenticated, try to get user info
                if (testResult) {
                    const userInfo = await this.getUserInfo();
                    if (userInfo) {
                        this.userInfo = userInfo;
                    }
                }
            } else {
                this.isAuthenticated = false;
                this.authStatus = 'not_authenticated';
            }
            return this.authStatus;
        } catch (error) {
            console.error('Error checking auth status:', error);
            this.isAuthenticated = false;
            this.authStatus = 'error';
            return this.authStatus;
        }
    }

    /**
     * Get user information from Google credentials
     */
    async getUserInfo() {
        try {
            const os = require('os');
            const path = require('path');
            const fs = require('fs').promises;

            // Path to cached credentials
            const credentialsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');

            try {
                const credentialsData = await fs.readFile(credentialsPath, 'utf8');
                const credentials = JSON.parse(credentialsData);

                if (credentials.id_token) {
                    // Decode the ID token to get user info
                    const tokenParts = credentials.id_token.split('.');
                    if (tokenParts.length === 3) {
                        const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());

                        return {
                            name: payload.name || payload.given_name || 'Google User',
                            email: payload.email,
                            picture: payload.picture,
                            sub: payload.sub
                        };
                    }
                }
            } catch (fileError) {
                console.log('No cached credentials found or invalid format');
            }

            return null;
        } catch (error) {
            console.error('Error getting user info:', error);
            return null;
        }
    }

    /**
     * Direct browser OAuth authentication (fast, no preliminary checks)
     */
    async openBrowserAuth() {
        try {
            console.log('🚀 Starting direct OAuth browser authentication...');

            // Immediately start the OAuth flow without any delays
            return await this.startOAuthFlow();

        } catch (error) {
            console.error('Error in direct browser auth:', error);
            return {
                success: false,
                error: error.message,
                authType: 'oauth_error'
            };
        }
    }

    /**
     * Sign out from Gemini CLI - removes all authentication data
     */
    async signOut() {
        try {
            console.log('Signing out from Gemini CLI...');

            // Stop any running processes
            this.stopCurrentProcess();

            // Remove OAuth credentials file
            const oauthCredsPath = path.join(this.configPath, 'oauth_creds.json');
            try {
                await fs.unlink(oauthCredsPath);
                console.log('Removed OAuth credentials file:', oauthCredsPath);
            } catch (error) {
                if (error.code !== 'ENOENT') {
                    console.error('Error removing OAuth credentials:', error);
                }
            }

            // Remove Google account ID file
            const googleAccountIdPath = path.join(this.configPath, 'google_account_id');
            try {
                await fs.unlink(googleAccountIdPath);
                console.log('Removed Google account ID file:', googleAccountIdPath);
            } catch (error) {
                if (error.code !== 'ENOENT') {
                    console.error('Error removing Google account ID:', error);
                }
            }

            // Clear internal state
            this.isAuthenticated = false;
            this.authStatus = 'not_authenticated';
            this.currentProcess = null;

            console.log('Successfully signed out from Gemini CLI');

            return {
                success: true,
                message: 'Successfully signed out from Gemini CLI'
            };

        } catch (error) {
            console.error('Error during sign out:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get rate limit statistics for Gemini CLI
     * Returns known Google free tier limits and estimated usage
     */
    async getStats(showLogs = false) {
        try {
            if (showLogs) {
                console.log('Getting Gemini CLI rate limit info...');
            }

            // Check if authenticated first
            if (!this.isAuthenticated) {
                return {
                    success: false,
                    error: 'Not authenticated with Gemini CLI'
                };
            }

            // Get current usage from our tracking (if available)
            const currentUsage = await this.getCurrentUsage();

            // Google free tier limits (as documented)
            const stats = {
                requests_per_minute_used: currentUsage.rpm || 0,
                requests_per_minute_limit: 60,
                requests_per_day_used: currentUsage.rpd || 0,
                requests_per_day_limit: 1000
            };

            if (showLogs) {
                console.log('Gemini CLI rate limit stats:', stats);
            }

            return {
                success: true,
                stats: stats
            };

        } catch (error) {
            console.error('Error getting Gemini stats:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get current usage estimates from local tracking
     */
    async getCurrentUsage() {
        try {
            const fs = require('fs');
            const path = require('path');
            const usageFile = path.join(process.cwd(), 'data', 'gemini_usage.json');

            // Create data directory if it doesn't exist
            const dataDir = path.dirname(usageFile);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // Read existing usage data
            let usageData = { requests: [] };
            if (fs.existsSync(usageFile)) {
                try {
                    const data = fs.readFileSync(usageFile, 'utf8');
                    usageData = JSON.parse(data);
                } catch (error) {
                    console.warn('Error reading usage file, using defaults:', error);
                }
            }

            const now = Date.now();
            const oneMinuteAgo = now - (60 * 1000);
            const oneDayAgo = now - (24 * 60 * 60 * 1000);

            // Filter requests within time windows
            const recentRequests = usageData.requests || [];
            const rpmCount = recentRequests.filter(timestamp => timestamp > oneMinuteAgo).length;
            const rpdCount = recentRequests.filter(timestamp => timestamp > oneDayAgo).length;

            return {
                rpm: rpmCount,
                rpd: rpdCount
            };
        } catch (error) {
            console.warn('Error getting current usage:', error);
            return { rpm: 0, rpd: 0 };
        }
    }

    /**
     * Record a Gemini CLI request for usage tracking
     */
    async recordRequest() {
        try {
            const fs = require('fs');
            const path = require('path');
            const usageFile = path.join(process.cwd(), 'data', 'gemini_usage.json');

            // Create data directory if it doesn't exist
            const dataDir = path.dirname(usageFile);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // Read existing usage data
            let usageData = { requests: [] };
            if (fs.existsSync(usageFile)) {
                try {
                    const data = fs.readFileSync(usageFile, 'utf8');
                    usageData = JSON.parse(data);
                } catch (error) {
                    console.warn('Error reading usage file, creating new:', error);
                }
            }

            const now = Date.now();
            const oneDayAgo = now - (24 * 60 * 60 * 1000);

            // Add current request and clean old requests (keep only last 24 hours)
            usageData.requests = usageData.requests || [];
            usageData.requests.push(now);
            usageData.requests = usageData.requests.filter(timestamp => timestamp > oneDayAgo);

            // Write back to file
            fs.writeFileSync(usageFile, JSON.stringify(usageData, null, 2));
            console.log('📊 Recorded Gemini CLI request for usage tracking');

            // Show updated stats after recording the request
            const updatedStats = await this.getStats(true);

        } catch (error) {
            console.warn('Error recording request:', error);
        }
    }



    /**
     * Check if Gemini CLI configuration exists
     */
    async checkConfigExists() {
        try {
            // Check for OAuth credentials file (the actual auth file used by Gemini CLI)
            const oauthCredsPath = path.join(this.configPath, 'oauth_creds.json');
            await fs.access(oauthCredsPath);
            console.log('Found Gemini OAuth credentials file:', oauthCredsPath);
            return true;
        } catch {
            console.log('No Gemini OAuth credentials found at:', path.join(this.configPath, 'oauth_creds.json'));
            return false;
        }
    }

    /**
     * Test authentication with a simple query
     */
    async testAuthentication() {
        return new Promise((resolve) => {
            console.log('Testing Gemini authentication...');

            // First check if OAuth credentials file exists
            const oauthCredsPath = path.join(this.configPath, 'oauth_creds.json');
            try {
                const fs = require('fs');
                if (fs.existsSync(oauthCredsPath)) {
                    const creds = JSON.parse(fs.readFileSync(oauthCredsPath, 'utf8'));
                    if (creds.access_token || creds.refresh_token) {
                        console.log('Found valid OAuth credentials');
                        resolve(true);
                        return;
                    }
                }
            } catch (error) {
                console.log('Error reading OAuth credentials:', error.message);
            }

            // Fallback to testing with a command
            const testProcess = spawn(this.geminiPath, this.getGeminiArgs(['-p', 'test']), {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            let stdout = '';
            let stderr = '';
            let authSuccess = false;

            testProcess.stdout.on('data', (data) => {
                const text = data.toString();
                stdout += text;
                console.log('Auth test stdout:', text);

                // Look for signs of successful response
                if (text.length > 10 && !text.includes('Please set an Auth method')) {
                    authSuccess = true;
                }
            });

            testProcess.stderr.on('data', (data) => {
                const text = data.toString();
                stderr += text;
                console.log('Auth test stderr:', text);

                // Check for authentication errors
                if (text.includes('Please set an Auth method') ||
                    text.includes('GEMINI_API_KEY') ||
                    text.includes('authentication required')) {
                    authSuccess = false;
                }
            });

            testProcess.on('close', (code) => {
                console.log('Auth test completed with code:', code);
                console.log('Auth test stdout:', stdout);
                console.log('Auth test stderr:', stderr);
                console.log('Auth success:', authSuccess);

                // Success if we got a response and no auth errors
                const isAuthenticated = authSuccess && code === 0 && !stderr.includes('Please set an Auth method');
                resolve(isAuthenticated);
            });

            testProcess.on('error', (error) => {
                console.error('Auth test process error:', error);
                resolve(false);
            });

            // Timeout fallback
            setTimeout(() => {
                if (!testProcess.killed) {
                    testProcess.kill();
                    resolve(false);
                }
            }, 15000);
        });
    }

    /**
     * Start authentication process with in-app browser
     */
    async startAuthentication() {
        try {
            console.log('Starting Gemini CLI authentication...');

            // First, check if authentication is already set up
            const authCheck = await this.checkAuthStatus();
            if (authCheck === 'authenticated') {
                return {
                    success: true,
                    message: 'Already authenticated with Gemini CLI'
                };
            }

            // Create the settings directory and file to trigger first-time setup
            return this.createInitialSettings();
        } catch (error) {
            console.error('Error in startAuthentication:', error);
            return {
                success: false,
                error: error.message,
                authType: 'oauth_error'
            };
        }
    }

    /**
     * Start proper Gemini CLI authentication flow (following the official process)
     */
    async createInitialSettings() {
        try {
            console.log('Starting proper Gemini CLI authentication flow...');

            // Step 1: Auto-select theme (Default Dark)
            // Step 2: Auto-select auth method (Login with Google)
            // Step 3: Start OAuth flow

            return await this.startOAuthFlow();
        } catch (error) {
            console.error('Error in createInitialSettings:', error);
            return {
                success: false,
                error: 'Authentication failed: ' + error.message,
                authType: 'oauth_error'
            };
        }
    }

    /**
     * Start the OAuth authentication flow (like the real Gemini CLI)
     */
    async startOAuthFlow() {
        try {
            console.log('🌐 Starting fast OAuth authentication flow...');

            // Create the authentication process that mimics Gemini CLI behavior
            return new Promise((resolve) => {
                const authProcess = spawn(this.geminiPath, this.getGeminiArgs(['-p', 'hello']), {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    shell: true,
                    timeout: 60000 // 1 minute timeout
                });

                let stdout = '';
                let stderr = '';
                let authUrlDetected = false;
                let waitingForAuth = false;

                authProcess.stdout.on('data', (data) => {
                    const text = data.toString();
                    stdout += text;
                    console.log('OAuth stdout:', text);

                    // Detect when browser should open (Gemini CLI behavior)
                    if (text.includes('Attempting to open authentication page') ||
                        text.includes('navigate to:') ||
                        text.includes('https://accounts.google.com')) {
                        authUrlDetected = true;
                        console.log('🌐 OAuth URL detected - browser should open');
                    }

                    // Detect waiting for auth state
                    if (text.includes('Waiting for auth') || text.includes('Waiting for authentication')) {
                        waitingForAuth = true;
                        console.log('⏳ Waiting for authentication in browser');
                        resolve({
                            success: false,
                            requiresBrowser: true,
                            message: 'Browser opened - complete sign-in in your browser',
                            authType: 'oauth_flow',
                            waitingForAuth: true
                        });
                    }

                    // Detect successful authentication
                    if ((text.includes('Using') && text.includes('files')) ||
                        text.includes('Hello! How can I help you today?') ||
                        text.includes('How can I help you') ||
                        text.includes('What would you like to') ||
                        (text.length > 20 && !text.includes('Waiting for') && !text.includes('navigate to'))) {
                        console.log('🎉 Authentication completed successfully!');
                        resolve({
                            success: true,
                            message: 'Authentication successful!',
                            authType: 'oauth_complete'
                        });
                    }
                });

                authProcess.stderr.on('data', (data) => {
                    const text = data.toString();
                    stderr += text;
                    console.log('OAuth stderr:', text);
                });

                authProcess.on('close', (code) => {
                    console.log('OAuth process completed with code:', code);

                    // Don't resolve immediately on close - let the authentication complete
                    if (!waitingForAuth && !authUrlDetected) {
                        resolve({
                            success: false,
                            error: 'Failed to start authentication process',
                            authType: 'oauth_error'
                        });
                    }
                    // If we're waiting for auth, keep the promise open for completion detection
                });

                authProcess.on('error', (error) => {
                    console.error('OAuth process error:', error);
                    resolve({
                        success: false,
                        error: error.message,
                        authType: 'oauth_error'
                    });
                });

                // Store the process for potential cleanup
                this.currentProcess = authProcess;

                // Give enough time for OAuth callback to complete
                setTimeout(() => {
                    if (!waitingForAuth && !authUrlDetected) {
                        authProcess.kill();
                        resolve({
                            success: false,
                            error: 'Browser opening timeout - please try again',
                            authType: 'oauth_timeout'
                        });
                    }
                }, 20000); // 20 seconds for browser opening

                // Longer timeout for OAuth completion
                setTimeout(() => {
                    if (waitingForAuth) {
                        console.log('OAuth completion timeout - checking credentials...');
                        // Don't kill process immediately, let it complete
                        resolve({
                            success: false,
                            requiresBrowser: true,
                            waitingForAuth: true,
                            message: 'Please complete sign-in in browser',
                            authType: 'oauth_waiting'
                        });
                    }
                }, 120000); // 2 minutes for OAuth completion
            });

        } catch (error) {
            console.error('Error starting OAuth flow:', error);
            return {
                success: false,
                error: error.message,
                authType: 'oauth_error'
            };
        }
    }

    /**
     * Start complete Gemini CLI authentication flow (exactly like terminal)
     */
    async startTerminalAuthentication() {
        try {
            console.log('Starting complete Gemini CLI authentication flow...');

            return new Promise((resolve, reject) => {
                // Run gemini command (just like in terminal)
                this.currentProcess = spawn(this.geminiPath, [], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    shell: true
                });

                let stdout = '';
                let stderr = '';
                let currentStep = 'starting';
                let authDetected = false;
                let authCompleted = false;

                this.currentProcess.stdout.on('data', (data) => {
                    const text = data.toString();
                    stdout += text;
                    console.log('Gemini stdout:', text);

                    // Step 1: Theme Selection
                    if (text.includes('Select Theme') && currentStep === 'starting') {
                        currentStep = 'theme_selection';
                        console.log('Theme selection detected - auto-selecting default...');
                        setTimeout(() => {
                            if (this.currentProcess && !this.currentProcess.killed) {
                                this.currentProcess.stdin.write('\n');
                            }
                        }, 1000);
                    }

                    // Step 2: Auth Method Selection
                    if (text.includes('Select Auth Method') && currentStep === 'theme_selection') {
                        currentStep = 'auth_method_selection';
                        console.log('Auth method selection detected - selecting Google login...');
                        setTimeout(() => {
                            if (this.currentProcess && !this.currentProcess.killed) {
                                this.currentProcess.stdin.write('\n');
                            }
                        }, 1000);
                    }

                    // Step 3: Waiting for Auth (Browser Opens)
                    if (text.includes('Waiting for auth') && !authDetected) {
                        authDetected = true;
                        currentStep = 'waiting_for_auth';
                        console.log('Authentication started - browser should open now');
                        resolve({
                            success: false,
                            authInProgress: true,
                            message: 'Browser opened - please complete Google sign-in',
                            requiresBrowserSignIn: true
                        });
                    }

                    // Step 4: Authentication Success
                    if ((text.includes('Using') && text.includes('files')) ||
                        text.includes('Type your message') ||
                        text.includes('gemini-2.5-pro')) {

                        if (!authCompleted) {
                            authCompleted = true;
                            currentStep = 'completed';
                            this.isAuthenticated = true;
                            this.authStatus = 'authenticated';
                            console.log('Authentication completed successfully!');

                            if (!authDetected) {
                                // Already authenticated
                                resolve({
                                    success: true,
                                    message: 'Already authenticated with Google!'
                                });
                            }
                        }
                    }
                });

                this.currentProcess.stderr.on('data', (data) => {
                    const text = data.toString();
                    stderr += text;
                    console.log('Gemini stderr:', text);

                    // Check for authentication errors
                    if (text.includes('Please set an Auth method') || text.includes('GEMINI_API_KEY')) {
                        reject({
                            success: false,
                            error: 'Gemini CLI needs authentication setup',
                            needsSetup: true
                        });
                    }
                });

                this.currentProcess.on('error', (error) => {
                    console.error('Gemini process error:', error);
                    reject({
                        success: false,
                        error: 'Failed to start Gemini CLI: ' + error.message
                    });
                });

                this.currentProcess.on('close', (code) => {
                    console.log('Gemini process closed with code:', code);
                    console.log('Final stdout:', stdout);
                    console.log('Final stderr:', stderr);
                    console.log('Final step:', currentStep);

                    if (!authDetected && !authCompleted) {
                        if (code === 0) {
                            // Process completed successfully but no auth detected
                            resolve({
                                success: true,
                                message: 'Gemini CLI is ready (may already be authenticated)'
                            });
                        } else {
                            reject({
                                success: false,
                                error: 'Gemini CLI failed to start: ' + (stderr || `Exit code ${code}`)
                            });
                        }
                    }
                });

                // Timeout after 3 minutes (longer for complete flow)
                setTimeout(() => {
                    if (this.currentProcess && !this.currentProcess.killed) {
                        this.currentProcess.kill();
                        if (!authDetected && !authCompleted) {
                            reject({
                                success: false,
                                error: 'Authentication timeout - please try again'
                            });
                        }
                    }
                }, 180000);
            });
        } catch (error) {
            console.error('Error in terminal authentication:', error);
            return {
                success: false,
                error: 'Authentication failed: ' + error.message
            };
        }
    }

    /**
     * Check if authentication completed (poll for completion)
     */
    async checkAuthenticationStatus() {
        try {
            console.log('Checking Gemini authentication status...');

            return new Promise((resolve) => {
                // Test with a simple prompt
                const testProcess = spawn(this.geminiPath, ['-p', 'test'], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    shell: true
                });

                let stdout = '';
                let stderr = '';
                let authSuccess = false;
                let authFailed = false;

                testProcess.stdout.on('data', (data) => {
                    const text = data.toString();
                    stdout += text;
                    console.log('Auth check stdout:', text);

                    // Look for signs of successful authentication
                    if ((text.includes('Using') && text.includes('files')) ||
                        text.includes('Type your message') ||
                        text.includes('gemini-2.5-pro') ||
                        text.length > 50) { // Any substantial response indicates auth success

                        authSuccess = true;
                        testProcess.kill();
                        this.isAuthenticated = true;
                        this.authStatus = 'authenticated';
                        resolve({
                            success: true,
                            message: 'Authentication verified successfully!'
                        });
                    }
                });

                testProcess.stderr.on('data', (data) => {
                    const text = data.toString();
                    stderr += text;
                    console.log('Auth check stderr:', text);

                    // Check for authentication errors
                    if (text.includes('Please set an Auth method') ||
                        text.includes('GEMINI_API_KEY') ||
                        text.includes('Waiting for auth')) {
                        authFailed = true;
                        testProcess.kill();
                        resolve({
                            success: false,
                            stillNeedsAuth: true,
                            message: 'Authentication not yet complete'
                        });
                    }
                });

                testProcess.on('close', (code) => {
                    console.log('Auth check process closed with code:', code);
                    console.log('Auth check stdout:', stdout);
                    console.log('Auth check stderr:', stderr);

                    if (!authSuccess && !authFailed) {
                        if (code === 0 && stdout.length > 10) {
                            // Successful execution with output
                            this.isAuthenticated = true;
                            this.authStatus = 'authenticated';
                            resolve({
                                success: true,
                                message: 'Authentication verified successfully!'
                            });
                        } else {
                            resolve({
                                success: false,
                                stillNeedsAuth: true,
                                message: 'Authentication not yet complete'
                            });
                        }
                    }
                });

                // Timeout after 15 seconds
                setTimeout(() => {
                    if (!testProcess.killed) {
                        testProcess.kill();
                        if (!authSuccess && !authFailed) {
                            resolve({
                                success: false,
                                stillNeedsAuth: true,
                                message: 'Authentication check timeout'
                            });
                        }
                    }
                }, 15000);
            });
        } catch (error) {
            console.error('Error checking authentication status:', error);
            return {
                success: false,
                error: 'Failed to check authentication status: ' + error.message
            };
        }
    }

    /**
     * Authenticate with Google using OAuth in embedded browser
     */
    async authenticateWithGoogle(email, password) {
        try {
            console.log('Starting Google OAuth authentication...');

            const { BrowserWindow } = require('electron');

            return new Promise((resolve, reject) => {
                // Create OAuth browser window
                const authWindow = new BrowserWindow({
                    width: 500,
                    height: 700,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true,
                        webSecurity: true
                    },
                    title: 'Sign in to Google',
                    autoHideMenuBar: true,
                    modal: true,
                    resizable: false
                });

                // Google OAuth URL for Gemini/AI Studio access
                const authUrl = 'https://accounts.google.com/oauth/authorize?' +
                    'client_id=************-6qr4p6gpi6hn506pt8ejuq83di341hur.apps.googleusercontent.com&' +
                    'redirect_uri=urn:ietf:wg:oauth:2.0:oob&' +
                    'response_type=code&' +
                    'scope=https://www.googleapis.com/auth/generative-language&' +
                    'access_type=offline&' +
                    'prompt=consent';

                authWindow.loadURL(authUrl);

                // Auto-fill credentials if provided
                if (email && password) {
                    authWindow.webContents.on('did-finish-load', () => {
                        const currentUrl = authWindow.webContents.getURL();

                        // Auto-fill email on Google sign-in page
                        if (currentUrl.includes('accounts.google.com')) {
                            setTimeout(() => {
                                authWindow.webContents.executeJavaScript(`
                                    const emailInput = document.querySelector('input[type="email"]') ||
                                                     document.querySelector('#identifierId') ||
                                                     document.querySelector('[name="identifier"]');

                                    if (emailInput && !emailInput.value) {
                                        emailInput.value = '${email}';
                                        emailInput.dispatchEvent(new Event('input', { bubbles: true }));

                                        // Click next button
                                        setTimeout(() => {
                                            const nextButton = document.querySelector('#identifierNext') ||
                                                             document.querySelector('[type="submit"]') ||
                                                             document.querySelector('button[jsname="LgbsSe"]');
                                            if (nextButton && !nextButton.disabled) {
                                                nextButton.click();
                                            }
                                        }, 500);
                                    }
                                `).catch(console.error);
                            }, 1000);
                        }

                        // Auto-fill password on password page
                        if (currentUrl.includes('challenge/pwd') || currentUrl.includes('password')) {
                            setTimeout(() => {
                                authWindow.webContents.executeJavaScript(`
                                    const passwordInput = document.querySelector('input[type="password"]') ||
                                                         document.querySelector('[name="password"]') ||
                                                         document.querySelector('#password');

                                    if (passwordInput && !passwordInput.value) {
                                        passwordInput.value = '${password}';
                                        passwordInput.dispatchEvent(new Event('input', { bubbles: true }));

                                        // Click sign in button
                                        setTimeout(() => {
                                            const signInButton = document.querySelector('#passwordNext') ||
                                                               document.querySelector('[type="submit"]') ||
                                                               document.querySelector('button[jsname="LgbsSe"]');
                                            if (signInButton && !signInButton.disabled) {
                                                signInButton.click();
                                            }
                                        }, 500);
                                    }
                                `).catch(console.error);
                            }, 1000);
                        }
                    });
                }

                // Handle successful authentication
                authWindow.webContents.on('will-navigate', (event, url) => {
                    this.handleAuthRedirect(url, authWindow, resolve, reject);
                });

                authWindow.webContents.on('did-navigate', (event, url) => {
                    this.handleAuthRedirect(url, authWindow, resolve, reject);
                });

                // Handle window closed
                authWindow.on('closed', () => {
                    reject({
                        success: false,
                        error: 'Authentication window was closed'
                    });
                });

                // Show the window
                authWindow.show();
                authWindow.focus();

                // Timeout after 10 minutes
                setTimeout(() => {
                    if (!authWindow.isDestroyed()) {
                        authWindow.close();
                        reject({
                            success: false,
                            error: 'Authentication timeout'
                        });
                    }
                }, 600000);
            });
        } catch (error) {
            console.error('Error in Google authentication:', error);
            return {
                success: false,
                error: 'Authentication failed: ' + error.message
            };
        }
    }

    /**
     * Handle OAuth redirect and extract authorization code
     */
    handleAuthRedirect(url, authWindow, resolve, reject) {
        console.log('Auth redirect URL:', url);

        // Check for authorization code in URL
        if (url.includes('code=')) {
            const urlParams = new URLSearchParams(url.split('?')[1] || url.split('#')[1]);
            const authCode = urlParams.get('code');

            if (authCode) {
                authWindow.close();
                this.exchangeCodeForTokens(authCode).then(resolve).catch(reject);
                return;
            }
        }

        // Check for success page with authorization code
        if (url.includes('success') || url.includes('approval')) {
            setTimeout(() => {
                authWindow.webContents.executeJavaScript(`
                    // Look for authorization code on the page
                    const codeElement = document.querySelector('input[readonly]') ||
                                      document.querySelector('textarea[readonly]') ||
                                      document.querySelector('code') ||
                                      document.querySelector('[data-copy-text]');

                    if (codeElement) {
                        return codeElement.value || codeElement.textContent || codeElement.innerText;
                    }

                    // Look for text that looks like an auth code
                    const bodyText = document.body.innerText;
                    const codeMatch = bodyText.match(/[A-Za-z0-9\-_\/]{20,}/);
                    return codeMatch ? codeMatch[0] : null;
                `).then((authCode) => {
                    if (authCode && authCode.length > 10) {
                        authWindow.close();
                        this.exchangeCodeForTokens(authCode).then(resolve).catch(reject);
                    }
                }).catch(console.error);
            }, 1000);
        }

        // Check for errors
        if (url.includes('error=')) {
            const urlParams = new URLSearchParams(url.split('?')[1] || url.split('#')[1]);
            const error = urlParams.get('error');
            const errorDescription = urlParams.get('error_description');

            authWindow.close();
            reject({
                success: false,
                error: `Authentication failed: ${error} - ${errorDescription}`
            });
        }
    }

    /**
     * Exchange authorization code for access tokens
     */
    async exchangeCodeForTokens(authCode) {
        try {
            // This would normally make an HTTP request to Google's token endpoint
            // For now, we'll simulate the token exchange and set up the Gemini CLI
            console.log('Exchanging authorization code for tokens...');

            // Create OAuth settings for Gemini CLI
            const fs = require('fs').promises;
            const path = require('path');
            const os = require('os');

            const geminiDir = path.join(os.homedir(), '.gemini');
            await fs.mkdir(geminiDir, { recursive: true });

            const settingsPath = path.join(geminiDir, 'settings.json');
            const settings = {
                "selectedAuthType": "oauth",
                "authCode": authCode,
                "timestamp": Date.now()
            };

            await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2));

            // Test the authentication
            const testResult = await this.testAuthentication();
            if (testResult) {
                this.isAuthenticated = true;
                this.authStatus = 'authenticated';
                return {
                    success: true,
                    message: 'Google authentication successful!'
                };
            } else {
                return {
                    success: false,
                    error: 'Authentication verification failed'
                };
            }
        } catch (error) {
            console.error('Error exchanging code for tokens:', error);
            return {
                success: false,
                error: 'Token exchange failed: ' + error.message
            };
        }
    }

    /**
     * Set up Gemini with API key
     */
    async setupWithApiKey(apiKey) {
        try {
            const fs = require('fs').promises;
            const path = require('path');
            const os = require('os');

            // Create .gemini directory if it doesn't exist
            const geminiDir = path.join(os.homedir(), '.gemini');
            await fs.mkdir(geminiDir, { recursive: true });

            // Create settings.json with API key
            const settingsPath = path.join(geminiDir, 'settings.json');
            const settings = {
                "selectedAuthType": "apikey",
                "apiKey": apiKey
            };

            await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2));
            console.log('Created settings with API key');

            // Test the setup
            const testResult = await this.testAuthentication();
            if (testResult) {
                this.isAuthenticated = true;
                this.authStatus = 'authenticated';
                return {
                    success: true,
                    message: 'API key setup successful!'
                };
            } else {
                return {
                    success: false,
                    error: 'API key test failed. Please check your key.'
                };
            }
        } catch (error) {
            console.error('Error setting up API key:', error);
            return {
                success: false,
                error: 'Failed to set up API key: ' + error.message
            };
        }
    }

    /**
     * Start the OAuth authentication flow
     */
    async startOAuthFlow() {
        return new Promise((resolve, reject) => {
            console.log('Starting OAuth flow...');

            // Run gemini with a simple prompt to trigger OAuth
            this.currentProcess = spawn(this.geminiPath, this.getGeminiArgs(['-p', 'hello']), {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';
            let authUrl = null;
            let hasFoundUrl = false;

            this.currentProcess.stdout.on('data', (data) => {
                const text = data.toString();
                stdout += text;
                console.log('OAuth stdout:', text);

                // Look for authentication URL
                const urlMatch = text.match(/(https:\/\/accounts\.google\.com[^\s\)\n]+)/);
                if (urlMatch && !hasFoundUrl) {
                    authUrl = urlMatch[1];
                    hasFoundUrl = true;
                    console.log('Found OAuth URL:', authUrl);

                    resolve({
                        success: false,
                        authUrl: authUrl,
                        message: 'Please complete authentication in browser',
                        requiresBrowser: true
                    });
                }

                // Check for successful authentication
                if (text.includes('Authentication successful') ||
                    text.includes('Hello') ||
                    text.includes('Welcome')) {
                    this.isAuthenticated = true;
                    this.authStatus = 'authenticated';
                    resolve({
                        success: true,
                        message: 'Authentication successful'
                    });
                }
            });

            this.currentProcess.stderr.on('data', (data) => {
                const text = data.toString();
                stderr += text;
                console.log('OAuth stderr:', text);

                // Look for authentication URL in stderr
                const urlMatch = text.match(/(https:\/\/accounts\.google\.com[^\s\)\n]+)/);
                if (urlMatch && !hasFoundUrl) {
                    authUrl = urlMatch[1];
                    hasFoundUrl = true;
                    console.log('Found OAuth URL in stderr:', authUrl);

                    resolve({
                        success: false,
                        authUrl: authUrl,
                        message: 'Please complete authentication in browser',
                        requiresBrowser: true
                    });
                }
            });

            this.currentProcess.on('error', (error) => {
                console.error('OAuth process error:', error);
                if (!hasFoundUrl) {
                    reject({
                        success: false,
                        error: error.message
                    });
                }
            });

            this.currentProcess.on('close', (code) => {
                console.log('OAuth process closed with code:', code);

                if (!hasFoundUrl) {
                    if (code === 0) {
                        // Success but no URL found - might be already authenticated
                        resolve({
                            success: true,
                            message: 'Authentication may already be complete'
                        });
                    } else {
                        resolve({
                            success: false,
                            error: 'OAuth flow failed: ' + stderr,
                            needsManualSetup: true
                        });
                    }
                }
            });

            // Timeout after 20 seconds
            setTimeout(() => {
                if (!hasFoundUrl && this.currentProcess && !this.currentProcess.killed) {
                    this.currentProcess.kill();
                    resolve({
                        success: false,
                        error: 'OAuth flow timeout',
                        needsManualSetup: true
                    });
                }
            }, 20000);
        });
    }

    /**
     * Poll for authentication completion
     */
    async pollForAuthCompletion() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 30; // 5 minutes with 10-second intervals

            const pollInterval = setInterval(async () => {
                attempts++;
                console.log(`Polling for auth completion, attempt ${attempts}/${maxAttempts}`);

                try {
                    const status = await this.checkAuthStatus();

                    if (status === 'authenticated') {
                        clearInterval(pollInterval);
                        this.isAuthenticated = true;
                        this.authStatus = 'authenticated';
                        resolve({
                            success: true,
                            message: 'Authentication successful!'
                        });
                    } else if (attempts >= maxAttempts) {
                        clearInterval(pollInterval);
                        resolve({
                            success: false,
                            error: 'Authentication timeout - please try again'
                        });
                    }
                } catch (error) {
                    console.error('Error polling auth status:', error);
                    if (attempts >= maxAttempts) {
                        clearInterval(pollInterval);
                        resolve({
                            success: false,
                            error: 'Authentication failed'
                        });
                    }
                }
            }, 10000); // Check every 10 seconds
        });
    }

    /**
     * Get setup instructions for the user
     */
    getSetupInstructions() {
        return {
            title: 'Gemini CLI Setup Required',
            steps: [
                '1. Open a new terminal/command prompt',
                '2. Run: gemini',
                '3. Follow the prompts to sign in with your Google account',
                '4. Once authenticated, return to this app and try again'
            ],
            note: 'This is a one-time setup. After authentication, Gemini CLI will work seamlessly with this app.'
        };
    }

    /**
     * Generate questions using Gemini CLI
     */
    async generateQuestions(content, options = {}) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Gemini CLI');
        }

        const {
            questionType = 'MCQ',
            questionCount = 5,
            difficulty = 'mixed',
            includeExplanations = true
        } = options;

        const prompt = this.buildQuestionPrompt(content, questionType, questionCount, difficulty, includeExplanations);

        return new Promise((resolve, reject) => {
            const process = spawn(this.geminiPath, this.getGeminiArgs(['-p', prompt]), {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let output = '';
            let errorOutput = '';

            process.stdout.on('data', (data) => {
                output += data.toString();
            });

            process.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    try {
                        const questions = this.parseQuestionResponse(output);
                        resolve({
                            success: true,
                            questions: questions,
                            rawOutput: output
                        });
                    } catch (parseError) {
                        reject({
                            success: false,
                            error: 'Failed to parse questions',
                            details: parseError.message,
                            rawOutput: output
                        });
                    }
                } else {
                    reject({
                        success: false,
                        error: `Gemini CLI failed with code ${code}`,
                        details: errorOutput
                    });
                }
            });

            process.on('error', (error) => {
                reject({
                    success: false,
                    error: 'Failed to start Gemini CLI',
                    details: error.message
                });
            });
        });
    }

    /**
     * Build prompt for question generation
     */
    buildQuestionPrompt(content, questionType, questionCount, difficulty, includeExplanations) {
        let prompt = `Generate ${questionCount} ${questionType} questions from the following content:\n\n${content}\n\n`;
        
        prompt += `Requirements:
- Question type: ${questionType}
- Difficulty: ${difficulty}
- Include explanations: ${includeExplanations ? 'Yes' : 'No'}
- Format as JSON with this structure:
{
  "questions": [
    {
      "question": "Question text",
      "options": ["A", "B", "C", "D"], // For MCQ only
      "answer": "Correct answer",
      "explanation": "Why this is correct",
      "difficulty": "easy|medium|hard"
    }
  ]
}`;

        return prompt;
    }

    /**
     * Parse question response from Gemini CLI
     */
    parseQuestionResponse(output) {
        try {
            // Look for JSON in the output
            const jsonMatch = output.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                const parsed = JSON.parse(jsonStr);
                return parsed.questions || [];
            }
            
            // Fallback: try to parse manually
            return this.parseQuestionsManually(output);
        } catch (error) {
            console.error('Error parsing questions:', error);
            throw new Error('Could not parse questions from response');
        }
    }

    /**
     * Manual parsing fallback
     */
    parseQuestionsManually(output) {
        // Simple regex-based parsing as fallback
        const questions = [];
        const lines = output.split('\n');
        
        let currentQuestion = null;
        for (const line of lines) {
            if (line.match(/^\d+\./)) {
                if (currentQuestion) {
                    questions.push(currentQuestion);
                }
                currentQuestion = {
                    question: line.replace(/^\d+\.\s*/, ''),
                    options: [],
                    answer: '',
                    explanation: '',
                    difficulty: 'medium'
                };
            } else if (line.match(/^[A-D]\)/)) {
                if (currentQuestion) {
                    currentQuestion.options.push(line);
                }
            } else if (line.toLowerCase().includes('answer:')) {
                if (currentQuestion) {
                    currentQuestion.answer = line.replace(/answer:\s*/i, '');
                }
            }
        }
        
        if (currentQuestion) {
            questions.push(currentQuestion);
        }
        
        return questions;
    }

    /**
     * Stop current process
     */
    stopCurrentProcess() {
        if (this.currentProcess && !this.currentProcess.killed) {
            this.currentProcess.kill();
            this.currentProcess = null;
        }
    }

    /**
     * Get authentication status
     */
    getAuthStatus() {
        return {
            isAuthenticated: this.isAuthenticated,
            status: this.authStatus
        };
    }

    /**
     * Fallback method: Open browser directly with Google OAuth
     */
    async openBrowserAuth() {
        try {
            console.log('Opening browser authentication directly...');

            const { shell } = require('electron');

            // Open Google OAuth URL directly in default browser
            const authUrl = 'https://accounts.google.com/oauth/authorize?' +
                'client_id=************-6qr4p6gpi6hn506pt8ejuq83di341hur.apps.googleusercontent.com&' +
                'redirect_uri=urn:ietf:wg:oauth:2.0:oob&' +
                'response_type=code&' +
                'scope=https://www.googleapis.com/auth/generative-language&' +
                'access_type=offline&' +
                'prompt=consent';

            await shell.openExternal(authUrl);

            return {
                success: false,
                browserOpened: true,
                message: 'Browser opened - please complete authentication',
                authUrl: authUrl
            };
        } catch (error) {
            console.error('Error opening browser auth:', error);
            return {
                success: false,
                error: 'Failed to open browser: ' + error.message
            };
        }
    }
}

module.exports = GeminiService;
