{"version": 3, "file": "turn.test.js", "sourceRoot": "", "sources": ["../../../src/core/turn.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACzE,OAAO,EACL,IAAI,EACJ,eAAe,GAGhB,MAAM,WAAW,CAAC;AAEnB,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAGzD,MAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACtC,MAAM,cAAc,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AAE/B,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IAChD,MAAM,MAAM,GAAG,MAAM,cAAc,EAAkC,CAAC;IACtE,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC;QACjD,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE,cAAc;KAC3B,CAAC,CAAC,CAAC;IACJ,OAAO;QACL,GAAG,MAAM;QACT,IAAI,EAAE,QAAQ;KACf,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,CAAC;IACxC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE;CACrB,CAAC,CAAC,CAAC;AAEJ,EAAE,CAAC,IAAI,CAAC,2CAA2C,EAAE,GAAG,EAAE,CAAC,CAAC;IAC1D,eAAe,EAAE,CAAC,IAA6B,EAAE,EAAE,CACjD,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;QACvE,SAAS;CACZ,CAAC,CAAC,CAAC;AAEJ,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;IACpB,IAAI,IAAU,CAAC;IAMf,IAAI,gBAAoC,CAAC;IAEzC,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QACnB,gBAAgB,GAAG;YACjB,iBAAiB,EAAE,qBAAqB;YACxC,UAAU,EAAE,cAAc;SAC3B,CAAC;QACF,IAAI,GAAG,IAAI,IAAI,CAAC,gBAAyC,CAAC,CAAC;QAC3D,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACnC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC,KAAK,SAAS,CAAC,MAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,eAAe,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;QACnB,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,kBAAkB,GAAG,CAAC,KAAK,SAAS,CAAC;gBACzC,MAAM;oBACJ,UAAU,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;iBACpB,CAAC;gBACxC,MAAM;oBACJ,UAAU,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;iBACrB,CAAC;YAC1C,CAAC,CAAC,EAAE,CAAC;YACL,qBAAqB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1C,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,CAChC,QAAQ,EACR,IAAI,eAAe,EAAE,CAAC,MAAM,CAC7B,EAAE,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,MAAM,CAAC,qBAAqB,CAAC,CAAC,oBAAoB,CAAC;gBACjD,OAAO,EAAE,QAAQ;gBACjB,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;aACjD,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,EAAE,IAAI,EAAE,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;gBACjD,EAAE,IAAI,EAAE,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;aACnD,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,kBAAkB,GAAG,CAAC,KAAK,SAAS,CAAC;gBACzC,MAAM;oBACJ,aAAa,EAAE;wBACb;4BACE,EAAE,EAAE,KAAK;4BACT,IAAI,EAAE,OAAO;4BACb,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;4BACtB,iBAAiB,EAAE,KAAK;yBACzB;wBACD,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,iBAAiB,EAAE,KAAK,EAAE,EAAE,QAAQ;qBAC9E;iBACoC,CAAC;YAC1C,CAAC,CAAC,EAAE,CAAC;YACL,qBAAqB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAW,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;YACjD,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,CAChC,QAAQ,EACR,IAAI,eAAe,EAAE,CAAC,MAAM,CAC7B,EAAE,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAqC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAC1B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtB,iBAAiB,EAAE,KAAK;aACzB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAqC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAC1B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtB,iBAAiB,EAAE,KAAK;aACzB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CACjC,MAAM,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAChD,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;YAC9C,MAAM,kBAAkB,GAAG,CAAC,KAAK,SAAS,CAAC;gBACzC,MAAM;oBACJ,UAAU,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;iBACzB,CAAC;gBACxC,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,MAAM;oBACJ,UAAU,EAAE;wBACV;4BACE,OAAO,EAAE;gCACP,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,uCAAuC,EAAE,CAAC;6BAC3D;yBACF;qBACF;iBACoC,CAAC;YAC1C,CAAC,CAAC,EAAE,CAAC;YACL,qBAAqB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;YAClD,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,EAAE,IAAI,EAAE,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE;gBACtD,EAAE,IAAI,EAAE,eAAe,CAAC,aAAa,EAAE;aACxC,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;YAC/E,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;YACrC,qBAAqB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAW,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;YACrD,MAAM,cAAc,GAAc;gBAChC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,EAAE;aACzD,CAAC;YACF,cAAc,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,CAChC,QAAQ,EACR,IAAI,eAAe,EAAE,CAAC,MAAM,CAC7B,EAAE,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAA2B,CAAC;YACvD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;gBAC/B,KAAK,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE;aACnD,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,WAAW,CAAC,CAAC,oBAAoB,CACtC,KAAK,EACL,kCAAkC,EAClC,CAAC,GAAG,cAAc,EAAE,QAAQ,CAAC,EAC7B,4BAA4B,CAC7B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,kBAAkB,GAAG,CAAC,KAAK,SAAS,CAAC;gBACzC,MAAM;oBACJ,aAAa,EAAE;wBACb,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;wBACtD,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC7C,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;qBAChD;iBACoC,CAAC;YAC1C,CAAC,CAAC,EAAE,CAAC;YACL,qBAAqB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAC5D,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAW,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;YACjE,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,CAChC,QAAQ,EACR,IAAI,eAAe,EAAE,CAAC,MAAM,CAC7B,EAAE,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAqC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAC1B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtB,iBAAiB,EAAE,KAAK;aACzB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAqC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAC1B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,EAAE;gBACR,iBAAiB,EAAE,KAAK;aACzB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAqC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAC1B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,EAAE;gBACR,iBAAiB,EAAE,KAAK;aACzB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG;gBACZ,UAAU,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;aACtB,CAAC;YACxC,MAAM,KAAK,GAAG;gBACZ,aAAa,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;aACD,CAAC;YACxC,MAAM,kBAAkB,GAAG,CAAC,KAAK,SAAS,CAAC;gBACzC,MAAM,KAAK,CAAC;gBACZ,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,EAAE,CAAC;YACL,qBAAqB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1C,IAAI,KAAK,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,eAAe,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvE,iBAAiB;YACnB,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}