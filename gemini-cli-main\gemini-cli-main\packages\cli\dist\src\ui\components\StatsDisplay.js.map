{"version": 3, "file": "StatsDisplay.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/StatsDisplay.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,QAAQ,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,eAAe,EAAgB,MAAM,+BAA+B,CAAC;AAC9E,OAAO,EACL,cAAc,EACd,sBAAsB,EACtB,wBAAwB,EACxB,wBAAwB,EACxB,0BAA0B,GAC3B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAQ/D,MAAM,OAAO,GAA2B,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC/D,MAAC,GAAG,eAEF,KAAC,GAAG,IAAC,KAAK,EAAE,EAAE,YACZ,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAAG,KAAK,GAAQ,GACzC,EACL,QAAQ,IACL,CACP,CAAC;AAQF,MAAM,UAAU,GAA8B,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACrE,MAAC,GAAG,IAAC,WAAW,EAAE,CAAC,aAEjB,KAAC,GAAG,IAAC,KAAK,EAAE,EAAE,YACZ,MAAC,IAAI,0BAAI,KAAK,IAAQ,GAClB,EACL,QAAQ,IACL,CACP,CAAC;AAQF,MAAM,OAAO,GAA2B,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC/D,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,YAAY,EAAE,CAAC,aACtD,KAAC,IAAI,IAAC,IAAI,kBAAE,KAAK,GAAQ,EACxB,QAAQ,IACL,CACP,CAAC;AAEF,MAAM,eAAe,GAIhB,CAAC,EAAE,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,EAAE,EAAE;IACtD,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,gBAAgB,GAAG,EAAE,CAAC;IAC5B,MAAM,iBAAiB,GAAG,EAAE,CAAC;IAE7B,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,aAEtC,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,KAAK,EAAE,SAAS,YACnB,KAAC,IAAI,IAAC,IAAI,kCAAmB,GACzB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,aAAa,EAAE,cAAc,EAAC,UAAU,YAClD,KAAC,IAAI,IAAC,IAAI,2BAAY,GAClB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,UAAU,YACrD,KAAC,IAAI,IAAC,IAAI,mCAAoB,GAC1B,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAC,UAAU,YACtD,KAAC,IAAI,IAAC,IAAI,oCAAqB,GAC3B,IACF,EAEN,KAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,YAAY,EAAE,IAAI,EAClB,SAAS,EAAE,KAAK,EAChB,UAAU,EAAE,KAAK,EACjB,WAAW,EAAE,KAAK,EAClB,KAAK,EAAE,SAAS,GAAG,aAAa,GAAG,gBAAgB,GAAG,iBAAiB,GAClE,EAGN,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,EAAE,CAAC,CACpD,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,KAAK,EAAE,SAAS,YACnB,KAAC,IAAI,cAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAQ,GACnC,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,aAAa,EAAE,cAAc,EAAC,UAAU,YAClD,KAAC,IAAI,cAAE,YAAY,CAAC,GAAG,CAAC,aAAa,GAAQ,GACzC,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,UAAU,YACrD,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAC7B,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,GACvC,GACH,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAC,UAAU,YACtD,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAC7B,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,GAC3C,GACH,KAhBE,IAAI,CAiBR,CACP,CAAC,EACD,eAAe,GAAG,CAAC,IAAI,CACtB,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,aACtC,MAAC,IAAI,eACH,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,WAAW,mCAA2B,EAAC,GAAG,EAC7D,iBAAiB,CAAC,cAAc,EAAE,QAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,sEAE5D,EACP,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAClB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,4EAEjB,IACH,CACP,IACG,CACP,CAAC;AACJ,CAAC,CAAC;AAOF,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EACxD,QAAQ,EACR,KAAK,GACN,EAAE,EAAE;IACH,MAAM,EAAE,KAAK,EAAE,GAAG,eAAe,EAAE,CAAC;IACpC,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IAC1B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAClC,MAAM,QAAQ,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAE9C,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,sBAAsB;QAC7B,MAAM,EAAE,wBAAwB;KACjC,CAAC;IACF,MAAM,mBAAmB,GAAG;QAC1B,KAAK,EAAE,wBAAwB;QAC/B,MAAM,EAAE,0BAA0B;KACnC,CAAC;IACF,MAAM,YAAY,GAAG,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;IAC7E,MAAM,cAAc,GAAG,cAAc,CACnC,QAAQ,CAAC,aAAa,EACtB,mBAAmB,CACpB,CAAC;IAEF,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CACjE,KAAC,QAAQ,IAAC,MAAM,EAAE,MAAM,CAAC,cAAc,YACrC,KAAC,IAAI,IAAC,IAAI,kBAAE,KAAK,GAAQ,GAChB,CACZ,CAAC,CAAC,CAAC,CACF,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAClC,KAAK,GACD,CACR,CAAC;QACJ,CAAC;QACD,OAAO,CACL,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,8BAE9B,CACR,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,QAAQ,EACtB,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,aAEV,WAAW,EAAE,EACd,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAEjB,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,CACvB,MAAC,OAAO,IAAC,KAAK,EAAC,qBAAqB,aAClC,KAAC,OAAO,IAAC,KAAK,EAAC,aAAa,YAC1B,MAAC,IAAI,eACF,KAAK,CAAC,UAAU,QAAI,GAAG,EACxB,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,WAAW,wBAAK,KAAK,CAAC,YAAY,IAAQ,EAAC,GAAG,EAClE,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,wBAAK,KAAK,CAAC,SAAS,IAAQ,UACpD,GACC,EACV,KAAC,OAAO,IAAC,KAAK,EAAC,eAAe,YAC5B,MAAC,IAAI,IAAC,KAAK,EAAE,YAAY,aAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,GAC5D,EACT,QAAQ,CAAC,cAAc,GAAG,CAAC,IAAI,CAC9B,KAAC,OAAO,IAAC,KAAK,EAAC,iBAAiB,YAC9B,MAAC,IAAI,IAAC,KAAK,EAAE,cAAc,aACxB,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,OAAG,GAAG,EACxC,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,kBACpB,QAAQ,CAAC,cAAc,kBACpB,IACF,GACC,CACX,IACO,CACX,EAED,MAAC,OAAO,IAAC,KAAK,EAAC,aAAa,aAC1B,KAAC,OAAO,IAAC,KAAK,EAAC,YAAY,YACzB,KAAC,IAAI,cAAE,QAAQ,GAAQ,GACf,EACV,KAAC,OAAO,IAAC,KAAK,EAAC,eAAe,YAC5B,KAAC,IAAI,cAAE,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAQ,GAC/C,EACV,KAAC,UAAU,IAAC,KAAK,EAAC,WAAW,YAC3B,MAAC,IAAI,eACF,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,GAAG,EAC3C,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,kBACpB,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,UAC/B,IACF,GACI,EACb,KAAC,UAAU,IAAC,KAAK,EAAC,YAAY,YAC5B,MAAC,IAAI,eACF,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,GAAG,EAC5C,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,kBACpB,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,UAChC,IACF,GACI,IACL,EAET,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CACjC,KAAC,eAAe,IACd,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,EAC7C,eAAe,EAAE,QAAQ,CAAC,eAAe,GACzC,CACH,IACG,CACP,CAAC;AACJ,CAAC,CAAC"}