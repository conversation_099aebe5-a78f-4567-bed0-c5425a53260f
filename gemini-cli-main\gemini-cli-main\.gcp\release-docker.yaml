steps:
  # Step 1: Install root dependencies (includes workspaces)
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Install Dependencies'
    entrypoint: 'npm'
    args: ['install']

  # Step 4: Authenticate for Docker (so we can push images to the artifact registry)
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Authenticate docker'
    entrypoint: 'npm'
    args: ['run', 'auth']

  # Step 5: Build workspace packages
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Build packages'
    entrypoint: 'npm'
    args: ['run', 'build:packages']

  # Step 6: Build sandbox container image
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Build sandbox Docker image'
    entrypoint: 'bash'
    args:
      - -c
      - |
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          export GEMINI_SANDBOX_IMAGE_TAG="${TAG_NAME#v}"
        else
          export GEMINI_SANDBOX_IMAGE_TAG="$SHORT_SHA"
        fi
        npm run build:sandbox:fast
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'

  # Step 7: Publish sandbox container image
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Publish sandbox Docker image'
    entrypoint: 'bash'
    args:
      - -c
      - |
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          export GEMINI_SANDBOX_IMAGE_TAG="${TAG_NAME#v}"
        else
          export GEMINI_SANDBOX_IMAGE_TAG="$SHORT_SHA"
        fi
        npm run publish:sandbox
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'

options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
  dynamicSubstitutions: true

substitutions:
  _OFFICIAL_RELEASE: 'false'
  _CONTAINER_TOOL: 'docker'
