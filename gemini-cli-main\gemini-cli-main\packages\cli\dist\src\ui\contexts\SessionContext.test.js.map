{"version": 3, "file": "SessionContext.test.js", "sourceRoot": "", "sources": ["../../../../src/ui/contexts/SessionContext.test.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,GAAG,EAAE,MAAM,sBAAsB,CAAC;AAC3C,OAAO,EACL,oBAAoB,EACpB,eAAe,GAEhB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AAClD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,WAAW,GAAG,CAAC,EACnB,UAAU,GAGX,EAAE,EAAE;IACH,UAAU,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAExC,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;QAC3E,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,MAAM,UAAU,GAAmB;YACjC,MAAM,EAAE;gBACN,YAAY,EAAE;oBACZ,GAAG,EAAE;wBACH,aAAa,EAAE,CAAC;wBAChB,WAAW,EAAE,CAAC;wBACd,cAAc,EAAE,GAAG;qBACpB;oBACD,MAAM,EAAE;wBACN,MAAM,EAAE,GAAG;wBACX,UAAU,EAAE,GAAG;wBACf,KAAK,EAAE,GAAG;wBACV,MAAM,EAAE,EAAE;wBACV,QAAQ,EAAE,EAAE;wBACZ,IAAI,EAAE,EAAE;qBACT;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,GAAG;gBACpB,cAAc,EAAE;oBACd,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;iBACV;gBACD,MAAM,EAAE;oBACN,WAAW,EAAE;wBACX,KAAK,EAAE,CAAC;wBACR,OAAO,EAAE,CAAC;wBACV,IAAI,EAAE,CAAC;wBACP,UAAU,EAAE,GAAG;wBACf,SAAS,EAAE;4BACT,MAAM,EAAE,CAAC;4BACT,MAAM,EAAE,CAAC;4BACT,MAAM,EAAE,CAAC;yBACV;qBACF;iBACF;aACF;SACF,CAAC;QAEF,GAAG,CAAC,GAAG,EAAE;YACP,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChC,OAAO,EAAE,UAAU;gBACnB,oBAAoB,EAAE,GAAG;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QACxC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0EAA0E,EAAE,GAAG,EAAE;QAClF,gEAAgE;QAChE,MAAM,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,6EAA6E;YAC7E,MAAM,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,OAAO,CAAC,4DAA4D,CAAC,CAAC;QAC3E,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}