{"version": 3, "file": "useGeminiStream.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useGeminiStream.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAC1E,OAAO,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC/B,OAAO,EAGL,eAAe,IAAI,qBAAqB,EAKxC,eAAe,EACf,WAAW,EACX,iBAAiB,EAEjB,aAAa,EACb,UAAU,EAGV,iBAAiB,EACjB,eAAe,GAChB,MAAM,yBAAyB,CAAC;AAEjC,OAAO,EACL,cAAc,EAId,WAAW,EACX,cAAc,GACf,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EACL,qBAAqB,EACrB,YAAY,IAAI,4BAA4B,GAI7C,MAAM,4BAA4B,CAAC;AAEpC,MAAM,UAAU,mBAAmB,CAAC,IAAqB;IACvD,MAAM,WAAW,GAAkB,EAAE,CAAC;IACtC,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,IAAK,sBAIJ;AAJD,WAAK,sBAAsB;IACzB,6EAAS,CAAA;IACT,qFAAa,CAAA;IACb,qEAAK,CAAA;AACP,CAAC,EAJI,sBAAsB,KAAtB,sBAAsB,QAI1B;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAC7B,YAA0B,EAC1B,OAAsB,EACtB,OAA2C,EAC3C,WAA0D,EAC1D,MAAc,EACd,cAAyC,EACzC,kBAIC,EACD,eAAwB,EACxB,kBAAgD,EAChD,WAAuB,EACvB,oBAAyC,EACzC,EAAE;IACF,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAChE,MAAM,kBAAkB,GAAG,MAAM,CAAyB,IAAI,CAAC,CAAC;IAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACvC,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAU,KAAK,CAAC,CAAC;IACjE,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAwB,IAAI,CAAC,CAAC;IACpE,MAAM,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,GAClD,cAAc,CAA8B,IAAI,CAAC,CAAC;IACpD,MAAM,uBAAuB,GAAG,MAAM,CAAc,IAAI,GAAG,EAAE,CAAC,CAAC;IAC/D,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE;QAC9B,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QACD,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;IACjD,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,CAAC,SAAS,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,GACxD,qBAAqB,CACnB,KAAK,EAAE,+BAA+B,EAAE,EAAE;QACxC,iFAAiF;QACjF,IAAI,+BAA+B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/C,iEAAiE;YACjE,OAAO,CACL,4BAA4B,CAC1B,+BAAoD,CACrD,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YAEF,kEAAkE;YAClE,MAAM,oBAAoB,CACxB,+BAAoD,CACrD,CAAC;QACJ,CAAC;IACH,CAAC,EACD,MAAM,EACN,qBAAqB,EACrB,kBAAkB,CACnB,CAAC;IAEJ,MAAM,2BAA2B,GAAG,OAAO,CACzC,GAAG,EAAE,CACH,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,EACxE,CAAC,SAAS,CAAC,CACZ,CAAC;IAEF,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,IAAmB,EAAE,EAAE;QACvD,eAAe,CAAC,IAAI,CAAC,CAAC;QACtB,MAAM,IAAI,CAAC;QACX,eAAe,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,MAAM,EAAE,kBAAkB,EAAE,GAAG,wBAAwB,CACrD,OAAO,EACP,qBAAqB,EACrB,MAAM,EACN,cAAc,EACd,MAAM,EACN,YAAY,CACb,CAAC;IAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,EAAE;QAClC,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,mBAAmB,CAAC,EAAE,CAAC;YAC9D,OAAO,cAAc,CAAC,sBAAsB,CAAC;QAC/C,CAAC;QACD,IACE,YAAY;YACZ,SAAS,CAAC,IAAI,CACZ,CAAC,EAAE,EAAE,EAAE,CACL,EAAE,CAAC,MAAM,KAAK,WAAW;gBACzB,EAAE,CAAC,MAAM,KAAK,WAAW;gBACzB,EAAE,CAAC,MAAM,KAAK,YAAY;gBAC1B,CAAC,CAAC,EAAE,CAAC,MAAM,KAAK,SAAS;oBACvB,EAAE,CAAC,MAAM,KAAK,OAAO;oBACrB,EAAE,CAAC,MAAM,KAAK,WAAW,CAAC;oBAC1B,CAAE,EAA0D;yBACzD,yBAAyB,CAAC,CAClC,EACD,CAAC;YACD,OAAO,cAAc,CAAC,UAAU,CAAC;QACnC,CAAC;QACD,OAAO,cAAc,CAAC,IAAI,CAAC;IAC7B,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;IAE9B,QAAQ,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;QACvB,IAAI,cAAc,KAAK,cAAc,CAAC,UAAU,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAC/D,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC7B,OAAO;YACT,CAAC;YACD,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YAChC,kBAAkB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;YACpC,IAAI,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBAClC,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,CACL;gBACE,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,IAAI,EAAE,oBAAoB;aAC3B,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACF,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC5B,eAAe,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,qBAAqB,GAAG,WAAW,CACvC,KAAK,EACH,KAAoB,EACpB,oBAA4B,EAC5B,WAAwB,EAIvB,EAAE;QACH,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;QACrD,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,wBAAwB,GAAyB,IAAI,CAAC;QAE1D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YAClC,aAAa,CACX,MAAM,EACN,IAAI,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,CACvD,CAAC;YACF,cAAc,CAAC,gBAAgB,YAAY,GAAG,CAAC,CAAC;YAChD,MAAM,MAAM,EAAE,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAE/D,gCAAgC;YAChC,MAAM,kBAAkB,GAAG,MAAM,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,OAAO,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,EAAE,CAAC;gBAClE,oEAAoE;gBACpE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;YACrD,CAAC;iBAAM,IACL,OAAO,kBAAkB,KAAK,QAAQ;gBACtC,kBAAkB,CAAC,kBAAkB,EACrC,CAAC;gBACD,kEAAkE;gBAClE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,kBAAkB,CAAC;gBAClD,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;oBACzB,MAAM,eAAe,GAAwB;wBAC3C,MAAM,EAAE,GAAG,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;wBAC1E,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,QAAQ;wBACd,iBAAiB,EAAE,IAAI;qBACxB,CAAC;oBACF,iBAAiB,CAAC,CAAC,eAAe,CAAC,EAAE,WAAW,CAAC,CAAC;gBACpD,CAAC;gBACD,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,iCAAiC;YACvF,CAAC;YAED,IAAI,eAAe,IAAI,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,CAAC;gBACrE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;YACrD,CAAC;YAED,qDAAqD;YACrD,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,MAAM,eAAe,GAAG,MAAM,eAAe,CAAC;oBAC5C,KAAK,EAAE,YAAY;oBACnB,MAAM;oBACN,OAAO;oBACP,cAAc;oBACd,SAAS,EAAE,oBAAoB;oBAC/B,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;gBACH,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;oBACnC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;gBACrD,CAAC;gBACD,wBAAwB,GAAG,eAAe,CAAC,cAAc,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,0BAA0B;gBAC1B,OAAO,CACL,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,EAC9C,oBAAoB,CACrB,CAAC;gBACF,wBAAwB,GAAG,YAAY,CAAC;YAC1C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,+DAA+D;YAC/D,wBAAwB,GAAG,KAAK,CAAC;QACnC,CAAC;QAED,IAAI,wBAAwB,KAAK,IAAI,EAAE,CAAC;YACtC,cAAc,CACZ,2DAA2D,CAC5D,CAAC;YACF,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,EAAE,WAAW,EAAE,wBAAwB,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;IACxE,CAAC,EACD;QACE,MAAM;QACN,OAAO;QACP,cAAc;QACd,kBAAkB;QAClB,kBAAkB;QAClB,MAAM;QACN,eAAe;QACf,iBAAiB;KAClB,CACF,CAAC;IAEF,gCAAgC;IAEhC,MAAM,kBAAkB,GAAG,WAAW,CACpC,CACE,UAAiC,EACjC,0BAAkC,EAClC,oBAA4B,EACpB,EAAE;QACV,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,4DAA4D;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,sBAAsB,GAAG,0BAA0B,GAAG,UAAU,CAAC;QACrE,IACE,qBAAqB,CAAC,OAAO,EAAE,IAAI,KAAK,QAAQ;YAChD,qBAAqB,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,EACxD,CAAC;YACD,IAAI,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBAClC,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YAC/D,CAAC;YACD,qBAAqB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YACpD,sBAAsB,GAAG,UAAU,CAAC;QACtC,CAAC;QACD,kEAAkE;QAClE,8DAA8D;QAC9D,MAAM,UAAU,GAAG,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAClE,IAAI,UAAU,KAAK,sBAAsB,CAAC,MAAM,EAAE,CAAC;YACjD,uDAAuD;YACvD,qBAAqB,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC/B,IAAI,EAAE,IAAI,EAAE,IAAmC;gBAC/C,IAAI,EAAE,sBAAsB;aAC7B,CAAC,CAAC,CAAC;QACN,CAAC;aAAM,CAAC;YACN,+DAA+D;YAC/D,2EAA2E;YAC3E,0EAA0E;YAC1E,wEAAwE;YACxE,+EAA+E;YAC/E,8EAA8E;YAC9E,8EAA8E;YAC9E,0DAA0D;YAC1D,MAAM,UAAU,GAAG,sBAAsB,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC/D,OAAO,CACL;gBACE,IAAI,EAAE,qBAAqB,CAAC,OAAO,EAAE,IAEjB;gBACpB,IAAI,EAAE,UAAU;aACjB,EACD,oBAAoB,CACrB,CAAC;YACF,qBAAqB,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;YACnE,sBAAsB,GAAG,SAAS,CAAC;QACrC,CAAC;QACD,OAAO,sBAAsB,CAAC;IAChC,CAAC,EACD,CAAC,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,CAAC,CACxD,CAAC;IAEF,MAAM,wBAAwB,GAAG,WAAW,CAC1C,CAAC,oBAA4B,EAAE,EAAE;QAC/B,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QACD,IAAI,qBAAqB,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,qBAAqB,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACxD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAC1D,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,OAAO;oBACtC,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,UAAU;oBACzC,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;oBACtC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC,QAAQ,EAAE;oBAC9C,CAAC,CAAC,IAAI,CACX,CAAC;gBACF,MAAM,WAAW,GAAyB;oBACxC,GAAG,qBAAqB,CAAC,OAAO;oBAChC,KAAK,EAAE,YAAY;iBACpB,CAAC;gBACF,OAAO,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YAC/D,CAAC;YACD,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,OAAO,CACL,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,6BAA6B,EAAE,EAC/D,oBAAoB,CACrB,CAAC;QACF,eAAe,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,EACD,CAAC,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,CAAC,CACxD,CAAC;IAEF,MAAM,gBAAgB,GAAG,WAAW,CAClC,CAAC,UAA+B,EAAE,oBAA4B,EAAE,EAAE;QAChE,IAAI,qBAAqB,CAAC,OAAO,EAAE,CAAC;YAClC,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YAC7D,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,OAAO,CACL;YACE,IAAI,EAAE,WAAW,CAAC,KAAK;YACvB,IAAI,EAAE,sBAAsB,CAC1B,UAAU,CAAC,KAAK,EAChB,MAAM,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAC5C;SACF,EACD,oBAAoB,CACrB,CAAC;IACJ,CAAC,EACD,CAAC,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,MAAM,CAAC,CAChE,CAAC;IAEF,MAAM,0BAA0B,GAAG,WAAW,CAC5C,CAAC,UAAoD,EAAE,EAAE,CACvD,OAAO,CACL;QACE,IAAI,EAAE,MAAM;QACZ,IAAI,EACF,qEAAqE,MAAM,CAAC,QAAQ,EAAE,IAAI;YAC1F,0EAA0E;YAC1E,GAAG,UAAU,EAAE,kBAAkB,IAAI,SAAS,MAAM;YACpD,GAAG,UAAU,EAAE,aAAa,IAAI,SAAS,WAAW;KACvD,EACD,IAAI,CAAC,GAAG,EAAE,CACX,EACH,CAAC,OAAO,EAAE,MAAM,CAAC,CAClB,CAAC;IAEF,MAAM,yBAAyB,GAAG,WAAW,CAC3C,KAAK,EACH,MAAkC,EAClC,oBAA4B,EAC5B,MAAmB,EACc,EAAE;QACnC,IAAI,mBAAmB,GAAG,EAAE,CAAC;QAC7B,MAAM,gBAAgB,GAA0B,EAAE,CAAC;QACnD,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,qBAAqB,CAAC,OAAO;oBAChC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,MAAM;gBACR,KAAK,qBAAqB,CAAC,OAAO;oBAChC,mBAAmB,GAAG,kBAAkB,CACtC,KAAK,CAAC,KAAK,EACX,mBAAmB,EACnB,oBAAoB,CACrB,CAAC;oBACF,MAAM;gBACR,KAAK,qBAAqB,CAAC,eAAe;oBACxC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACnC,MAAM;gBACR,KAAK,qBAAqB,CAAC,aAAa;oBACtC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;oBAC/C,MAAM;gBACR,KAAK,qBAAqB,CAAC,KAAK;oBAC9B,gBAAgB,CAAC,KAAK,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;oBACpD,MAAM;gBACR,KAAK,qBAAqB,CAAC,cAAc;oBACvC,0BAA0B,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxC,MAAM;gBACR,KAAK,qBAAqB,CAAC,oBAAoB,CAAC;gBAChD,KAAK,qBAAqB,CAAC,gBAAgB;oBACzC,aAAa;oBACb,MAAM;gBACR,OAAO,CAAC,CAAC,CAAC;oBACR,kCAAkC;oBAClC,MAAM,WAAW,GAAU,KAAK,CAAC;oBACjC,OAAO,WAAW,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,iBAAiB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,sBAAsB,CAAC,SAAS,CAAC;IAC1C,CAAC,EACD;QACE,kBAAkB;QAClB,wBAAwB;QACxB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;KAC3B,CACF,CAAC;IAEF,MAAM,WAAW,GAAG,WAAW,CAC7B,KAAK,EAAE,KAAoB,EAAE,OAAqC,EAAE,EAAE;QACpE,IACE,CAAC,cAAc,KAAK,cAAc,CAAC,UAAU;YAC3C,cAAc,KAAK,cAAc,CAAC,sBAAsB,CAAC;YAC3D,CAAC,OAAO,EAAE,cAAc;YAExB,OAAO;QAET,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEnB,kBAAkB,CAAC,OAAO,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC;QACtD,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;QAEjC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,MAAM,qBAAqB,CAChE,KAAK,EACL,oBAAoB,EACpB,WAAW,CACZ,CAAC;QAEF,IAAI,CAAC,aAAa,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YAC3C,OAAO;QACT,CAAC;QAED,eAAe,CAAC,IAAI,CAAC,CAAC;QACtB,YAAY,CAAC,IAAI,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACxE,MAAM,gBAAgB,GAAG,MAAM,yBAAyB,CACtD,MAAM,EACN,oBAAoB,EACpB,WAAW,CACZ,CAAC;YAEF,IAAI,gBAAgB,KAAK,sBAAsB,CAAC,aAAa,EAAE,CAAC;gBAC9D,OAAO;YACT,CAAC;YAED,IAAI,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBAClC,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;gBAC7D,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,KAAK,YAAY,iBAAiB,EAAE,CAAC;gBACvC,WAAW,EAAE,CAAC;YAChB,CAAC;iBAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC9D,OAAO,CACL;oBACE,IAAI,EAAE,WAAW,CAAC,KAAK;oBACvB,IAAI,EAAE,sBAAsB,CAC1B,eAAe,CAAC,KAAK,CAAC,IAAI,eAAe,EACzC,MAAM,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAC5C;iBACF,EACD,oBAAoB,CACrB,CAAC;YACJ,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,eAAe,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACH,CAAC,EACD;QACE,cAAc;QACd,WAAW;QACX,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,OAAO;QACP,qBAAqB;QACrB,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,MAAM;KACP,CACF,CAAC;IAEF,MAAM,oBAAoB,GAAG,WAAW,CACtC,KAAK,EAAE,+BAAkD,EAAE,EAAE;QAC3D,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,MAAM,8BAA8B,GAClC,+BAA+B,CAAC,MAAM,CACpC,CACE,EAAmB,EACwC,EAAE;YAC7D,MAAM,eAAe,GACnB,EAAE,CAAC,MAAM,KAAK,SAAS;gBACvB,EAAE,CAAC,MAAM,KAAK,OAAO;gBACrB,EAAE,CAAC,MAAM,KAAK,WAAW,CAAC;YAE5B,IAAI,eAAe,EAAE,CAAC;gBACpB,MAAM,wBAAwB,GAAG,EAEL,CAAC;gBAC7B,OAAO,CACL,wBAAwB,CAAC,QAAQ,EAAE,aAAa,KAAK,SAAS,CAC/D,CAAC;YACJ,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CACF,CAAC;QAEJ,gEAAgE;QAChE,MAAM,WAAW,GAAG,8BAA8B,CAAC,MAAM,CACvD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CACnC,CAAC;QACF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,4EAA4E;QAC5E,MAAM,wBAAwB,GAAG,8BAA8B,CAAC,MAAM,CACpE,CAAC,CAAC,EAAE,EAAE,CACJ,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa;YAChC,CAAC,CAAC,MAAM,KAAK,SAAS;YACtB,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CACzD,CAAC;QAEF,IAAI,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,kDAAkD;YAClD,KAAK,oBAAoB,EAAE,CAAC;YAC5B,uEAAuE;YACvE,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CACrC,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CACtD,CAAC;QACJ,CAAC;QAED,MAAM,WAAW,GAAG,8BAA8B,CAAC,MAAM,CACvD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CACpC,CAAC;QAEF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,sEAAsE;QACtE,MAAM,iBAAiB,GAAG,WAAW,CAAC,KAAK,CACzC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,WAAW,CAClC,CAAC;QAEF,IAAI,iBAAiB,EAAE,CAAC;YACtB,IAAI,YAAY,EAAE,CAAC;gBACjB,gEAAgE;gBAChE,+CAA+C;gBAC/C,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CACxC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAC9C,CAAC;gBACF,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;oBACtC,IAAI,KAAa,CAAC;oBAClB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,KAAK,GAAG,QAAQ,CAAC;oBACnB,CAAC;yBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;wBACxC,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;oBAC/B,CAAC;yBAAM,CAAC;wBACN,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACrB,CAAC;oBACD,YAAY,CAAC,UAAU,CAAC;wBACtB,IAAI,EAAE,MAAM;wBACZ,KAAK;qBACN,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,MAAM,wBAAwB,GAAG,WAAW,CAAC,GAAG,CAC9C,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CACtC,CAAC;YACF,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;YAC/C,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAoB,WAAW,CAAC,GAAG,CACtD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAC9C,CAAC;QACF,MAAM,wBAAwB,GAAG,WAAW,CAAC,GAAG,CAC9C,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CACtC,CAAC;QAEF,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAC/C,WAAW,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE;YAChD,cAAc,EAAE,IAAI;SACrB,CAAC,CAAC;IACL,CAAC,EACD;QACE,YAAY;QACZ,WAAW;QACX,oBAAoB;QACpB,YAAY;QACZ,oBAAoB;KACrB,CACF,CAAC;IAEF,MAAM,mBAAmB,GAAG;QAC1B,qBAAqB,CAAC,OAAO;QAC7B,2BAA2B;KAC5B,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;IAE/C,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,uBAAuB,GAAG,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,EAAE,CAAC;gBACtC,OAAO;YACT,CAAC;YACD,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CAC1C,CAAC,QAAQ,EAAE,EAAE,CACX,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;gBAClC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,CAAC;gBACzC,QAAQ,CAAC,MAAM,KAAK,mBAAmB,CAC1C,CAAC;YAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,MAAM,aAAa,GAAG,MAAM,CAAC,iBAAiB,EAAE;oBAC9C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,aAAa,CAAC;oBACtD,CAAC,CAAC,SAAS,CAAC;gBAEd,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACrD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACnD,cAAc,CACZ,0CAA0C,eAAe,CAAC,KAAK,CAAC,EAAE,CACnE,CAAC;wBACF,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,mBAAmB,EAAE,CAAC;oBAC3C,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAW,CAAC;oBAC9D,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACd,cAAc,CACZ,2DAA2D,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,CACnF,CAAC;wBACF,SAAS;oBACX,CAAC;oBAED,IAAI,CAAC;wBACH,IAAI,UAAU,GAAG,MAAM,UAAU,EAAE,kBAAkB,CACnD,gBAAgB,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,CACxC,CAAC;wBAEF,IAAI,CAAC,UAAU,EAAE,CAAC;4BAChB,UAAU,GAAG,MAAM,UAAU,EAAE,oBAAoB,EAAE,CAAC;wBACxD,CAAC;wBAED,IAAI,CAAC,UAAU,EAAE,CAAC;4BAChB,cAAc,CACZ,iCAAiC,QAAQ,kCAAkC,CAC5E,CAAC;4BACF,SAAS;wBACX,CAAC;wBAED,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE;6BACzB,WAAW,EAAE;6BACb,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;6BAClB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;wBACvB,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;wBACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACzC,MAAM,4BAA4B,GAAG,GAAG,SAAS,IAAI,QAAQ,IAAI,QAAQ,OAAO,CAAC;wBACjF,MAAM,aAAa,GAAG,MAAM,YAAY,EAAE,UAAU,EAAE,CAAC;wBACvD,MAAM,4BAA4B,GAAG,IAAI,CAAC,IAAI,CAC5C,aAAa,EACb,4BAA4B,CAC7B,CAAC;wBAEF,MAAM,EAAE,CAAC,SAAS,CAChB,4BAA4B,EAC5B,IAAI,CAAC,SAAS,CACZ;4BACE,OAAO;4BACP,aAAa;4BACb,QAAQ,EAAE;gCACR,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;gCAC3B,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;6BAC5B;4BACD,UAAU;4BACV,QAAQ;yBACT,EACD,IAAI,EACJ,CAAC,CACF,CACF,CAAC;oBACJ,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,cAAc,CACZ,8CAA8C,eAAe,CAC3D,KAAK,CACN,EAAE,CACJ,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QACF,uBAAuB,EAAE,CAAC;IAC5B,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;IAE3E,OAAO;QACL,cAAc;QACd,WAAW;QACX,SAAS;QACT,mBAAmB;QACnB,OAAO;KACR,CAAC;AACJ,CAAC,CAAC"}