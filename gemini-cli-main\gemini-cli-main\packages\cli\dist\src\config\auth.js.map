{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/config/auth.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAE9C,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,UAAkB,EAAiB,EAAE;IACtE,eAAe,EAAE,CAAC;IAClB,IAAI,UAAU,KAAK,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,UAAU,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,OAAO,uGAAuG,CAAC;QACjH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,UAAU,KAAK,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC1C,MAAM,8BAA8B,GAClC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAC5E,MAAM,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;QACrD,IAAI,CAAC,8BAA8B,IAAI,CAAC,eAAe,EAAE,CAAC;YACxD,OAAO,CACL,2DAA2D;gBAC3D,2EAA2E;gBAC3E,kEAAkE;gBAClE,mDAAmD,CACpD,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,+BAA+B,CAAC;AACzC,CAAC,CAAC"}