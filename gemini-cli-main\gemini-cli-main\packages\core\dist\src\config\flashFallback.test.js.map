{"version": 3, "file": "flashFallback.test.js", "sourceRoot": "", "sources": ["../../../src/config/flashFallback.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,oBAAoB,EAAE,0BAA0B,EAAE,MAAM,aAAa,CAAC;AAE/E,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAClD,IAAI,MAAc,CAAC;IAEnB,UAAU,CAAC,GAAG,EAAE;QACd,MAAM,GAAG,IAAI,MAAM,CAAC;YAClB,SAAS,EAAE,cAAc;YACzB,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,KAAK;YAChB,GAAG,EAAE,OAAO;YACZ,KAAK,EAAE,oBAAoB;SAC5B,CAAC,CAAC;QAEH,gDAAgD;QAE9C,MACD,CAAC,sBAAsB,GAAG;YACzB,KAAK,EAAE,oBAAoB;YAC3B,QAAQ,EAAE,gBAAgB;SAC3B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE1D,MAAM,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,4DAA4D;YAC5D,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC;gBAC3B,SAAS,EAAE,gBAAgB;gBAC3B,SAAS,EAAE,OAAO;gBAClB,SAAS,EAAE,KAAK;gBAChB,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,oBAAoB;aAC5B,CAAC,CAAC;YAEH,4DAA4D;YAC5D,SAAS,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,gDAAgD;YAChD,MAAM,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6EAA6E,EAAE,GAAG,EAAE;YACrF,uEAAuE;YACvE,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC;gBAC3B,SAAS,EAAE,gBAAgB;gBAC3B,SAAS,EAAE,OAAO;gBAClB,SAAS,EAAE,KAAK;gBAChB,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,cAAc;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,8DAA8D;YAC9D,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,8CAA8C;YAC9C,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,wBAAwB;YACxB,MAAM,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,mBAAmB;YACnB,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAE7B,8CAA8C;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,4DAA4D;YAC5D,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC;gBAC3B,SAAS,EAAE,gBAAgB;gBAC3B,SAAS,EAAE,OAAO;gBAClB,SAAS,EAAE,KAAK;gBAChB,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,oBAAoB;aAC5B,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}