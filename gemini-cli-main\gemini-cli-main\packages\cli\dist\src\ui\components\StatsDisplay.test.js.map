{"version": 3, "file": "StatsDisplay.test.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/StatsDisplay.test.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,KAAK,cAAc,MAAM,+BAA+B,CAAC;AAGhE,0DAA0D;AAC1D,EAAE,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IAChE,MAAM,MAAM,GAAG,MAAM,cAAc,EAAyB,CAAC;IAC7D,OAAO;QACL,GAAG,MAAM;QACT,eAAe,EAAE,EAAE,CAAC,EAAE,EAAE;KACzB,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,MAAM,mBAAmB,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AAEtE,MAAM,qBAAqB,GAAG,CAAC,OAAuB,EAAE,EAAE;IACxD,mBAAmB,CAAC,eAAe,CAAC;QAClC,KAAK,EAAE;YACL,gBAAgB,EAAE,IAAI,IAAI,EAAE;YAC5B,OAAO;YACP,oBAAoB,EAAE,CAAC;SACxB;KACF,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,KAAC,YAAY,IAAC,QAAQ,EAAC,IAAI,GAAG,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,WAAW,GAAmB;YAClC,MAAM,EAAE,EAAE;YACV,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,WAAW,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAE3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QACpD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB;QAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,MAAM,OAAO,GAAmB;YAC9B,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE;oBAChE,MAAM,EAAE;wBACN,MAAM,EAAE,IAAI;wBACZ,UAAU,EAAE,IAAI;wBAChB,KAAK,EAAE,KAAK;wBACZ,MAAM,EAAE,GAAG;wBACX,QAAQ,EAAE,GAAG;wBACb,IAAI,EAAE,EAAE;qBACT;iBACF;gBACD,kBAAkB,EAAE;oBAClB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE;oBAC/D,MAAM,EAAE;wBACN,MAAM,EAAE,KAAK;wBACb,UAAU,EAAE,KAAK;wBACjB,KAAK,EAAE,SAAS;wBAChB,MAAM,EAAE,KAAK;wBACb,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAE3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAClC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,MAAM,OAAO,GAAmB;YAC9B,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE;oBAC9D,MAAM,EAAE;wBACN,MAAM,EAAE,GAAG;wBACX,UAAU,EAAE,GAAG;wBACf,KAAK,EAAE,GAAG;wBACV,MAAM,EAAE,EAAE;wBACV,QAAQ,EAAE,CAAC;wBACX,IAAI,EAAE,CAAC;qBACR;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,GAAG;gBACpB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE;oBACN,WAAW,EAAE;wBACX,KAAK,EAAE,CAAC;wBACR,OAAO,EAAE,CAAC;wBACV,IAAI,EAAE,CAAC;wBACP,UAAU,EAAE,GAAG;wBACf,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;qBAC/C;iBACF;aACF;SACF,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAE3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QAChD,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAAmB;gBAC9B,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE;oBACL,UAAU,EAAE,CAAC;oBACb,YAAY,EAAE,CAAC;oBACf,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,GAAG;oBACpB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,eAAe;oBACpE,MAAM,EAAE;wBACN,WAAW,EAAE;4BACX,KAAK,EAAE,CAAC;4BACR,OAAO,EAAE,CAAC;4BACV,IAAI,EAAE,CAAC;4BACP,UAAU,EAAE,GAAG;4BACf,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;yBAC/C;qBACF;iBACF;aACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAE3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAAmB;gBAC9B,MAAM,EAAE;oBACN,gBAAgB,EAAE;wBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE;wBAC9D,MAAM,EAAE;4BACN,MAAM,EAAE,GAAG;4BACX,UAAU,EAAE,GAAG;4BACf,KAAK,EAAE,GAAG;4BACV,MAAM,EAAE,CAAC;4BACT,QAAQ,EAAE,CAAC;4BACX,IAAI,EAAE,CAAC;yBACR;qBACF;iBACF;gBACD,KAAK,EAAE;oBACL,UAAU,EAAE,CAAC;oBACb,YAAY,EAAE,CAAC;oBACf,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACnD,MAAM,EAAE,EAAE;iBACX;aACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAE3B,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAmB;gBAC9B,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE;oBACL,UAAU,EAAE,EAAE;oBACd,YAAY,EAAE,EAAE;oBAChB,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACnD,MAAM,EAAE,EAAE;iBACX;aACF,CAAC;YACF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAmB;gBAC9B,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE;oBACL,UAAU,EAAE,EAAE;oBACd,YAAY,EAAE,CAAC;oBACf,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACnD,MAAM,EAAE,EAAE;iBACX;aACF,CAAC;YACF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAmB;gBAC9B,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE;oBACL,UAAU,EAAE,EAAE;oBACd,YAAY,EAAE,CAAC;oBACf,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACnD,MAAM,EAAE,EAAE;iBACX;aACF,CAAC;YACF,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,MAAM,WAAW,GAAmB;YAClC,MAAM,EAAE,EAAE;YACV,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC;QAEF,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,WAAW,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,mBAAmB,CAAC,eAAe,CAAC;gBAClC,KAAK,EAAE;oBACL,gBAAgB,EAAE,IAAI,IAAI,EAAE;oBAC5B,OAAO,EAAE,WAAW;oBACpB,oBAAoB,EAAE,CAAC;iBACxB;aACF,CAAC,CAAC;YAEH,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,YAAY,IAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,+BAA+B,GAAG,CACrE,CAAC;YACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}