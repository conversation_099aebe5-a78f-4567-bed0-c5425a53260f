{"version": 3, "file": "useLoadingIndicator.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useLoadingIndicator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC,eAAe;AAEpE,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,cAA8B,EAAE,EAAE;IACpE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtD,MAAM,aAAa,GAAG,cAAc,KAAK,cAAc,CAAC,UAAU,CAAC;IAEnE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAEpE,MAAM,qBAAqB,GAAG,cAAc,KAAK,cAAc,CAAC,UAAU,CAAC;IAC3E,MAAM,SAAS,GAAG,cAAc,KAAK,cAAc,CAAC,sBAAsB,CAAC;IAC3E,MAAM,oBAAoB,GAAG,eAAe,CAC1C,qBAAqB,EACrB,SAAS,CACV,CAAC;IAEF,MAAM,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClE,MAAM,qBAAqB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAElE,SAAS,CAAC,GAAG,EAAE;QACb,IACE,qBAAqB,CAAC,OAAO,KAAK,cAAc,CAAC,sBAAsB;YACvE,cAAc,KAAK,cAAc,CAAC,UAAU,EAC5C,CAAC;YACD,gBAAgB,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAC3C,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,oDAAoD;QACjF,CAAC;aAAM,IACL,cAAc,KAAK,cAAc,CAAC,IAAI;YACtC,qBAAqB,CAAC,OAAO,KAAK,cAAc,CAAC,UAAU,EAC3D,CAAC;YACD,gBAAgB,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,iDAAiD;YAC7F,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,cAAc,KAAK,cAAc,CAAC,sBAAsB,EAAE,CAAC;YACpE,wDAAwD;YACxD,kFAAkF;YAClF,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;QAC/C,CAAC;QAED,qBAAqB,CAAC,OAAO,GAAG,cAAc,CAAC;IACjD,CAAC,EAAE,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAE3C,OAAO;QACL,WAAW,EACT,cAAc,KAAK,cAAc,CAAC,sBAAsB;YACtD,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,oBAAoB;QAC1B,oBAAoB;KACrB,CAAC;AACJ,CAAC,CAAC"}