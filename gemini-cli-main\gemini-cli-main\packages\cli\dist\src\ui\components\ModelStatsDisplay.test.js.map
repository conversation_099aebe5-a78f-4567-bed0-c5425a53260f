{"version": 3, "file": "ModelStatsDisplay.test.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/ModelStatsDisplay.test.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AAClD,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,KAAK,cAAc,MAAM,+BAA+B,CAAC;AAGhE,0DAA0D;AAC1D,EAAE,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IAChE,MAAM,MAAM,GAAG,MAAM,cAAc,EAAyB,CAAC;IAC7D,OAAO;QACL,GAAG,MAAM;QACT,eAAe,EAAE,EAAE,CAAC,EAAE,EAAE;KACzB,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,MAAM,mBAAmB,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AAEtE,MAAM,qBAAqB,GAAG,CAAC,OAAuB,EAAE,EAAE;IACxD,mBAAmB,CAAC,eAAe,CAAC;QAClC,KAAK,EAAE;YACL,gBAAgB,EAAE,IAAI,IAAI,EAAE;YAC5B,OAAO;YACP,oBAAoB,EAAE,CAAC;SACxB;KACF,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,KAAC,iBAAiB,KAAG,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;QAC9E,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC;YAC1C,MAAM,EAAE,EAAE;YACV,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAC3B,8CAA8C,CAC/C,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;QAC3E,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC;YAC1C,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE;oBAC9D,MAAM,EAAE;wBACN,MAAM,EAAE,EAAE;wBACV,UAAU,EAAE,EAAE;wBACd,KAAK,EAAE,EAAE;wBACT,MAAM,EAAE,CAAC;wBACT,QAAQ,EAAE,CAAC;wBACX,IAAI,EAAE,CAAC;qBACR;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACvC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;QACxE,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC;YAC1C,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE;oBAC9D,MAAM,EAAE;wBACN,MAAM,EAAE,EAAE;wBACV,UAAU,EAAE,EAAE;wBACd,KAAK,EAAE,EAAE;wBACT,MAAM,EAAE,CAAC;wBACT,QAAQ,EAAE,CAAC;wBACX,IAAI,EAAE,CAAC;qBACR;iBACF;gBACD,kBAAkB,EAAE;oBAClB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,EAAE,EAAE;oBAC7D,MAAM,EAAE;wBACN,MAAM,EAAE,CAAC;wBACT,UAAU,EAAE,EAAE;wBACd,KAAK,EAAE,EAAE;wBACT,MAAM,EAAE,CAAC;wBACT,QAAQ,EAAE,CAAC;wBACX,IAAI,EAAE,CAAC;qBACR;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;QAC5D,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC;YAC1C,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE;oBAChE,MAAM,EAAE;wBACN,MAAM,EAAE,GAAG;wBACX,UAAU,EAAE,GAAG;wBACf,KAAK,EAAE,GAAG;wBACV,MAAM,EAAE,EAAE;wBACV,QAAQ,EAAE,EAAE;wBACZ,IAAI,EAAE,CAAC;qBACR;iBACF;gBACD,kBAAkB,EAAE;oBAClB,GAAG,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE;oBAC/D,MAAM,EAAE;wBACN,MAAM,EAAE,GAAG;wBACX,UAAU,EAAE,GAAG;wBACf,KAAK,EAAE,GAAG;wBACV,MAAM,EAAE,GAAG;wBACX,QAAQ,EAAE,EAAE;wBACZ,IAAI,EAAE,EAAE;qBACT;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;QACpE,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC;YAC1C,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE;wBACH,aAAa,EAAE,SAAS;wBACxB,WAAW,EAAE,SAAS;wBACtB,cAAc,EAAE,IAAI;qBACrB;oBACD,MAAM,EAAE;wBACN,MAAM,EAAE,SAAS;wBACjB,UAAU,EAAE,SAAS;wBACrB,KAAK,EAAE,SAAS;wBAChB,MAAM,EAAE,SAAS;wBACjB,QAAQ,EAAE,SAAS;wBACnB,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,MAAM,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC;YAC1C,MAAM,EAAE;gBACN,gBAAgB,EAAE;oBAChB,GAAG,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE;oBAC9D,MAAM,EAAE;wBACN,MAAM,EAAE,EAAE;wBACV,UAAU,EAAE,EAAE;wBACd,KAAK,EAAE,EAAE;wBACT,MAAM,EAAE,CAAC;wBACT,QAAQ,EAAE,CAAC;wBACX,IAAI,EAAE,CAAC;qBACR;iBACF;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACjD,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}